/* eslint-disable no-constant-condition */
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';

/*
The `findExistingClientEmailsByEmail` function lookups in the 'ClientEmails' table using 'email' and returns 'ClientEmails' object.

Parameters:
- supabase: The Supabase client instance used for making queries.
- email: The email to look up in the 'client_emails' table.
*/
const findExistingClientEmailsByEmail = async (email: string) => {
  const { data, error } = await supabase
    .from('client_emails')
    .select(`*`)
    .eq('email', email);
  if (error) throw error;
  return data[0];
};

/* 
The `createNewClientEmails` function creates a new 'ClientEmails' entry with the provided 'client_id' and 'email'. 
It first checks if the 'email' already exists in the 'client_emails' table by calling the 'findExistingClientEmailsByEmail' function. 
If the email exists, it returns the existing entry. 
Otherwise, it creates a new entry and returns it.

Parameters:
- supabase: The Supabase client instance used for making queries.
- client_id: The ID of the client associated with the email.
- email: The email to create a new 'client_emails' entry for.
*/
const createNewClientEmails = async (
  client_id: number,
  email: string,
  organization_id: any
) => {
  // if there is no email, then give an error
  if (!email) throw new Error('Missing email');

  // if there is no client_id, then give an error
  if (!client_id) throw new Error('Missing client_id');

  // Check if the email already exists in the 'client_emails' table
  const existing = await findExistingClientEmailsByEmail(email);

  // If the email already exists, return the existing entry
  if (existing) {
    return existing;
  } else {
    // If the email doesn't exist, create a new 'client_emails' entry using the client_id and email

    const { data, error } = await supabase
      .from('client_emails')
      .upsert(
        {
          client_id: client_id,
          email: email,
          organization_id: organization_id,
          is_primary_email: false,
        },
        // { onConflict: 'email', ignoreDuplicates: true }
        { onConflict: 'organization_id,email', ignoreDuplicates: true }
      )
      .select();

    if (error) throw error;

    // Return the newly created 'client_emails' entry
    return data[0];
  }
};

/*
The `findExistingClientByInitialEmail` function queries the 'clients' table using the provided 'email' as the 'email'.
It returns the first matching client object if it exists.

Parameters:
- supabase: The Supabase client instance used for making queries.
- email: The initial email to look up in the 'clients' table.
*/
const findExistingClientByInitialEmail = async (email: string) => {
  // const raw = localStorage.getItem('UserState');
  // const dataOrg = raw ? JSON.parse(raw) : null;
  // const org = dataOrg?.UserState?.organization;
  // Query the 'clients' table for entries with the provided initial email

  const { data, error } = await supabase
    .from(tableNames.client_emails)
    .select(`clients(*), email`)
    .eq('email', email);
  // .eq('organization_id', Number(org?.id));
  if (error) throw error;

  if (!data || data.length === 0) return null;

  const { clients, email: clientEmail } = data[0];
  return { ...clients, email: clientEmail };
};

/*
The `createNewClient` function creates a new client entry with the provided 'clientObj' properties. 
It first checks if a client with the same email already exists by calling the 'findExistingClientByInitialEmail' function. 
If the client exists, it uses the existing client object; otherwise, it creates a new client entry. 
After creating the client, it calls the 'createNewClientEmails' function to create the 'client_emails' entry for the client's email.

Parameters:
- supabase: The Supabase client instance used for making queries.
- clientObj: An object containing the properties of the new client to be created.

clientObj = {
  email*,
  first_name*,
  last_name,
  stage*,
}
*/
const createNewClient = async (clientObj: any) => {
  if (!clientObj.email) throw new Error('Missing email');
  // Find existing client id by email
  const existing = await findExistingClientByInitialEmail(clientObj.email);
  let client;
  if (existing) {
    // If the client with the provided email already exists, use the existing client
    client = existing;
  } else {
    // If the client with the provided email doesn't exist, create a new client entry
    const { data, error } = await supabase
      .from('clients')
      .insert({
        //email: clientObj.email,
        first_name: clientObj.first_name,
        last_name: clientObj.last_name,
        stage: clientObj.stage,
      })
      .select();
    if (error) throw error;

    // Get the newly created client object and log it for debugging purposes
    client = data[0];
  }

  await createNewClientEmails(
    client.id,
    clientObj.email,
    client.organization_id
  );

  return client;
};

/*
The `findAllBookingsByClientId` function queries the 'bookings' table using the provided 'client_id'. 
It returns an array of all bookings associated with that client.

Parameters:
- supabase: The Supabase client instance used for making queries.
- client_id: The ID of the client to find associated bookings for.
*/
const findAllBookingsByClientId = async (client_id: number) => {
  // Query the 'bookings' table for entries with the provided client_id
  const { data, error } = await supabase
    .from('bookings')
    .select(`*`)
    .is('client_id', client_id);
  if (error) throw error;
  return data;
};

/*
The `findAllInvoicesByClientId` function queries the 'invoices' table using the provided 'client_id'. 
It returns an array of all invoices associated with that client.

Parameters:
- supabase: The Supabase client instance used for making queries.
- client_id: The ID of the client to find associated invoices for.
*/
const findAllInvoicesByClientId = async (client_id: number) => {
  // Query the 'invoices' table for entries with the provided client_id
  const { data, error } = await supabase
    .from('invoices')
    .select(`*`)
    .is('client_id', client_id);
  if (error) throw error;
  return data;
};

/*
The `findExistingClientEmails` function queries the 'client_emails' table using the provided 'email'.
It returns an array of all 'client_emails' that match the provided email.

Parameters:
- supabase: The Supabase client instance used for making queries.
- email: The email to look up in the 'client_emails' table.
*/
const findExistingClientEmails = async (email: string) => {
  // Query the 'client_emails' table for entries with the provided email
  const { data, error } = await supabase
    .from('client_emails')
    .select(`*`)
    .eq('email', email);

  if (error) throw error;
  return data;
};

/*
The `updateClientIdOnBookingsById` function updates the 'client_id' on the bookings table for a given 'booking_id'.

Parameters:
- supabase: The Supabase client instance used for making queries.
- booking_id: The ID of the booking to update the 'client_id' for.
- client_id: The ID of the client to set for the 'client_id' field of the booking.
*/
const updateClientIdOnBookingsById = async (
  booking_id: number,
  client_id: number
) => {
  if (!booking_id || !client_id) throw new Error('Missing Info');

  // Update the 'client_id' for the booking with the provided 'booking_id'
  const { error } = await supabase
    .from('bookings')
    .update({ client_id: client_id })
    .eq('id', booking_id);

  if (error) throw error;
};

/*
The `updateClientIdOnInvoicesById` function updates the 'client_id' on the invoices table for a given 'invoice_id'.

Parameters:
- supabase: The Supabase client instance used for making queries.
- invoice_id: The ID of the invoice to update the 'client_id' for.
- client_id: The ID of the client to set for the 'client_id' field of the invoice.
*/
const updateClientIdOnInvoicesById = async (
  invoice_id: number,
  client_id: number
) => {
  if (!invoice_id || !client_id) throw new Error('Missing Info');

  // Update the 'client_id' for the invoice with the provided 'invoice_id'
  const { error } = await supabase
    .from('invoices')
    .update({ client_id: client_id })
    .eq('id', invoice_id);

  if (error) throw error;
};

const findAllInvoicesWithMissingDuration = async () => {
  const { data, error } = await supabase
    .from('invoices')
    .select('*')
    .is('duration', null);
  if (error) throw error;
  return data;
};

const findAllRawAppointmentsFromBookings = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
      .is('appointment', null) // Add this filter condition
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

const findAllRawBookingCreatedAtFromBookings = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
      .is('created_at', null) // Add this filter condition
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

const findAllInvoiceDatesFromInvoices = async () => {
  let allData: any = [];
  const fetchLimit = 1000; // Number of records to fetch per iteration
  let offset = 0;

  while (true) {
    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .is('invoice_date', null) // Add this filter condition
      .range(offset, offset + fetchLimit - 1);

    if (error) throw error;

    if (data.length === 0) {
      break; // No more data, break out of the loop
    }

    allData = allData.concat(data);
    offset += fetchLimit;
  }

  return allData;
};

const findSlpById = async (slpId: number) => {
  const { data, error } = await supabase
    .from(tableNames.slps)
    .select('*')
    .eq('is', null);
  if (error) throw slpId;
  return data;
};

export {
  createNewClient,
  createNewClientEmails,
  findAllBookingsByClientId,
  findAllInvoiceDatesFromInvoices,
  findAllInvoicesByClientId,
  findAllInvoicesWithMissingDuration,
  findAllRawAppointmentsFromBookings,
  findAllRawBookingCreatedAtFromBookings,
  findExistingClientByInitialEmail,
  findExistingClientEmails,
  findSlpById,
  updateClientIdOnBookingsById,
  updateClientIdOnInvoicesById,
};
