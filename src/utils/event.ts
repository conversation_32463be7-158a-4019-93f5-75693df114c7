export const getSlugFromOrgName = (eventName: string) => {
  return eventName
    .toLowerCase()
    ?.trim()
    ?.replace(/[^a-z0-9]+/g, '-')
    ?.replace(/^-+|-+$/g, '');
};
// export const getSlugFromName = (eventName: string) => {
//   const baseSlug = eventName
//     .toLowerCase()
//     .trim()
//     .replace(/[^a-z0-9]+/g, '-')
//     .replace(/^-+|-+$/g, '');

//   const randomSuffix = Math.random().toString(36).substring(2, 8);
//   return `${baseSlug}-${randomSuffix}`;
// };
export const getSlugFromName = (eventName: string) => {
  // Return empty string if input is empty or only whitespace
  if (!eventName || !eventName.trim()) {
    return '';
  }

  const baseSlug = eventName
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  // If after processing we get an empty string, return empty
  if (!baseSlug) {
    return '';
  }

  // const randomSuffix = Math.random().toString(36).substring(2, 8);
  return `${baseSlug}`;
};
