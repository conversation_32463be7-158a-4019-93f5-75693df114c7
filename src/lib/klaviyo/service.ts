import { tableNames } from '@/constants/table_names';
import { SupabaseClient } from '@supabase/supabase-js';
import { KlaviyoAPI, KlaviyoEvent } from './api';
import { KlaviyoActions } from '@/constants/klaviyo-actions';

export const getKlaviyoOrganization = async (
  organization_id: string,
  supabase: SupabaseClient
) => {
  const { data: KlaviyoDetails, error: DetailsError } = await supabase
    .from(tableNames.klaviyo_organization)
    .select('*')
    .eq('organization_id', organization_id)
    .maybeSingle();
  if (DetailsError) throw DetailsError;
  return KlaviyoDetails;
};
export const dispatchBookingCreatedEvent = async (
  organization_id: string,
  supabase: SupabaseClient,
  event: any
) => {
  try {
    const KlaviyoDetails = await getKlaviyoOrganization(
      organization_id,
      supabase
    );

    const klaviyoApi = new KlaviyoAPI(
      KlaviyoDetails?.access_token,
      KlaviyoDetails?.refresh_token
    );
    await klaviyoApi.trackEvent(
      organization_id,
      KlaviyoActions.BOOKING_CREATED,
      event,
      supabase
    );
  } catch (klaviyoError: any) {
    console.warn('Failed to track Klaviyo event in route:', klaviyoError);
  }
};
export const dispatchKlaviyoEvent = async (
  organization_id: string,
  supabase: SupabaseClient,
  event: KlaviyoEvent,
  eventType: string
) => {
  try {
    const KlaviyoDetails = await getKlaviyoOrganization(
      organization_id,
      supabase
    );
    const klaviyoApi = new KlaviyoAPI(
      KlaviyoDetails?.access_token,
      KlaviyoDetails?.refresh_token
    );
    const { data: ClientDetails } = await supabase
      .from(tableNames.clients)
      .select('*')
      .eq('id', event?.data?.attributes?.profile?.data?.attributes?.id)
      .maybeSingle();
    // console.log(
    //   'profile is ',
    //   event?.data?.attributes?.profile?.data?.attributes
    // );
    // console.log('ClientDetails is ', ClientDetails);
    const updatedProps = {
      ...event?.data?.attributes?.profile?.data?.attributes?.properties,
      ...(ClientDetails?.province && { province: ClientDetails.province }),
      ...(ClientDetails?.country?.name && {
        country: ClientDetails.country.name,
      }),
      ...(ClientDetails?.state?.name && { state: ClientDetails.state.name }),
      ...(ClientDetails?.city?.name && { city: ClientDetails.city.name }),
      ...(ClientDetails?.address && { address: ClientDetails.address }),
      ...(ClientDetails?.phone && { phone: ClientDetails.phone }),
      ...(ClientDetails?.utm_source && {
        utm_source: ClientDetails.utm_source,
      }),
      ...(ClientDetails?.utm_medium && {
        utm_medium: ClientDetails.utm_medium,
      }),
      ...(ClientDetails?.utm_campaign && {
        utm_campaign: ClientDetails.utm_campaign,
      }),
      ...(ClientDetails?.utm_content && {
        utm_content: ClientDetails.utm_content,
      }),
    };

    event.data.attributes.profile.data.attributes.properties = updatedProps;
    delete event.data.attributes.profile.data.attributes.id;

    await klaviyoApi.trackEvent(organization_id, eventType, event, supabase);
  } catch (klaviyoError: any) {
    console.warn('Failed to track Klaviyo event in route:', klaviyoError);
  }
};
