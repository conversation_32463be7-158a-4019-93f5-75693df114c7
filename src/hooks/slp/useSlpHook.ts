/* eslint-disable react-hooks/exhaustive-deps */
import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import { useGetUserBySlugQuery } from '@/api/users/find-by-event-slug';
import { useUpdateUserAndOrganizationApi } from '@/api/users/update-user-and-organization';
import { useUpdateUserSlugMutation } from '@/api/users/update-username-slug';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import { toaster } from '@/components/ui/toaster';
import { bucketNames } from '@/constants/bucket-names';
import { queryKey } from '@/constants/query-key';
import supabase from '@/lib/supabase/client';
import { UserState } from '@/store/user/user';
import { useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import { useCountryStateCityHook } from '../countryStateCity/useCountryStateCityHook';

export const useSlpHook = (id: number) => {
  const {
    data,
    isFetching,
    isLoading: dIsLoading,
    refetch,
  } = useGetUserByIdQuery(Number(id), {
    enabled: Boolean(id),
  });

  // console.log('data---555667', data);

  const [isEdit, setIsEdit] = useState(true);
  const [eventForm, setEventForm] = useState<any>({
    tempSearch: data?.event_slug,
    search: '',
  });
  const [organizationForm, setOrganizationForm] = useState<any>({
    tempSearch: data?.organization?.slug,
    search: '',
  });
  const initialTaxOption = {
    name: '',
    rate: null,
  };
  const [autoSubmit, setAutoSubmit] = useState(false);
  const [taxOption, setTaxOption] = useState(initialTaxOption);
  const { mutateAsync, isLoading } = useUpdateUserAndOrganizationApi();
  const { mutateAsync: UpdateUserSlug, isLoading: UpdateUserSlugLoading } =
    useUpdateUserSlugMutation();

  const queryClient = useQueryClient();
  const { open, onClose, onOpen } = useDisclosure();
  const [userState, setUserState] = useRecoilState(UserState);
  const [isUploading, setIsUploading] = useState(false);
  const [payload, setPayload] = useState<any>({
    targetHr: data?.target_hr_per_month || 0,
    timezone: data?.timezone,
    name: data?.organization?.name,
    currency_code: data?.organization?.currency_code,
    billing_info: data?.organization?.billing_info,
    first_name: data?.first_name,
    last_name: data?.last_name,
    office_title: data?.office_title,
    entity: '',
    regNum: '',
    logoFile: null, // Holds the uploaded logo file
    address: data?.organization?.billing_info?.address,
    country: data?.organization?.billing_info?.country,
    city: data?.organization?.billing_info?.city,
    phone: data?.organization?.billing_info?.phone,
    website: data?.organization?.billing_info?.website,
    state: data?.organization?.billing_info?.state,
    taxes: data?.organization?.taxes?.length ? data?.organization?.taxes : [],
  });
  const {
    data: UserBySlugData,
    isLoading: UserBySlugLoading,
    isSuccess: UserBySlugSuccess,
  } = useGetUserBySlugQuery(
    { slug: eventForm.search, isPublic: 'true' },
    {
      enabled: Boolean(eventForm.search),
    }
  );
  const { countryOptions, stateOptions, cityOptions } = useCountryStateCityHook(
    {
      countryCode: payload?.country?.isoCode,
      stateCode: payload?.state?.isoCode,
    }
  );

  const {
    data: OrganizationBySlugData,
    isLoading: OrganizationBySlugLoading,
    isSuccess: OrganizationBySlugSuccess,
  } = useGetOrganizationBySlugQuery(
    { slug: organizationForm.search, isPublic: 'true' },
    {
      enabled: Boolean(organizationForm.search),
    }
  );

  const handleChange = (e: any, key: string) => {
    let value;

    if (e?.target) {
      value = e.target.value;
    } else {
      value = e;
    }

    if (key === 'targetHr' && isNaN(value)) {
      return;
    }

    // Handle empty values properly
    if (value === '' || value === null || value === undefined) {
      setPayload((prev: any) => ({ ...prev, [key]: '' }));
      return;
    }

    setPayload((prev: any) => ({ ...prev, [key]: value }));
  };

  const uploadLogo = async (file: File) => {
    try {
      setIsUploading(true); // Start loading when upload begins

      const fileName = `${Date.now()}-${file.name}`;
      const { data: uploadData, error } = await supabase.storage
        .from(bucketNames.organizations_logo)
        .upload(fileName, file);

      if (error) {
        toaster.create({ description: 'Failed to upload logo', type: 'error' });
        return null;
      }

      const { data: publicUrlData } = supabase.storage
        .from(bucketNames.organizations_logo)
        .getPublicUrl(uploadData.path);

      return publicUrlData.publicUrl;
    } catch (error) {
      console.error('Error uploading logo:', error);
      return null;
    } finally {
      setIsUploading(false); // Stop loading when upload completes
    }
  };

  const addTax = () => {
    if (!taxOption.name || [null, undefined].includes(taxOption.rate)) {
      return;
    }
    const taxes = payload.taxes ? [...payload.taxes] : [];

    taxes.push(taxOption);
    handleChange(taxes, 'taxes');
    setTaxOption(initialTaxOption);
    setAutoSubmit(true);
  };

  const removeTax = (taxKey: any) => {
    const taxes = payload.taxes ?? [];
    const keptTaxes = taxes.filter((_: any, index: any) => taxKey !== index);
    handleChange(keptTaxes, 'taxes');
    setAutoSubmit(true);
  };

  const handleSubmit = async () => {
    let logoUrl = data?.organization?.logo_url; // Default to existing logo

    if (payload.logoFile) {
      const uploadedLogoUrl = await uploadLogo(payload.logoFile);

      if (uploadedLogoUrl) {
        logoUrl = uploadedLogoUrl;
      }
    }

    const reg =
      payload.entity && payload.regNum
        ? `${payload.entity} ${payload.regNum}`
        : null;
    const registrationSet = new Set(
      [...(data?.registration || []), reg].filter(Boolean)
    );

    const updatePayload = {
      data: {
        user: {
          target_hr_per_month: payload.targetHr || data?.target_hr_per_month,
          timezone: payload.timezone || data?.timezone,
          first_name: payload.first_name || data?.first_name,
          last_name: payload.last_name || data?.last_name,
          office_title: payload?.office_title || data?.office_title,
          registration: Array.from(registrationSet),
          ...(UserBySlugSuccess &&
            UserBySlugData?.length === 0 &&
            eventForm.search
            ? { event_slug: eventForm.search }
            : {}),
        },
        org: {
          name: payload.name || data?.organization?.name,
          currency_code:
            payload?.currency_code || data?.organization?.currency_code,
          taxes: payload?.taxes,
          contact_stages: payload?.contact_stages,
          billing_info: {
            ...(payload.billing_info || data?.organization?.billing_info),
            address: payload?.address,
            city: payload?.city,
            website: payload?.website,
            phone: payload?.phone,
            state: payload?.state,
            country: payload?.country,
          },
          id: data?.organization_id,
          logo_url: logoUrl,
          ...(OrganizationBySlugSuccess &&
            OrganizationBySlugData?.length === 0 &&
            organizationForm.search
            ? { slug: organizationForm.search }
            : {}),
        },
      },
      id: data?.id,
    };
    // console.log('updatePayload is ', updatePayload);
    // return;

    await mutateAsync(updatePayload);
    if (UserBySlugSuccess && UserBySlugData?.length === 0 && eventForm.search) {
      await UpdateUserSlug({ id, new_slug: eventForm.search });
    }
    queryClient.invalidateQueries([queryKey.users.getByEmail, data?.email]);

    const { data: reData } = await refetch();

    setUserState({ ...userState, ...reData });
    // setIsEdit(false);
    onClose();
  };

  useEffect(() => {
    if (
      !eventForm?.tempSearch?.trim() ||
      eventForm?.tempSearch === data?.event_slug
    ) {
      setEventForm((prev: any) => ({ ...prev, search: '' }));
      return;
    }
    const debounceTimer = setTimeout(() => {
      setEventForm((prev: any) => ({ ...prev, search: prev.tempSearch }));
    }, 600);

    return () => clearTimeout(debounceTimer);
  }, [data?.event_slug, eventForm?.tempSearch]);

  useEffect(() => {
    if (
      !organizationForm?.tempSearch?.trim() ||
      organizationForm?.tempSearch === data?.organization?.slug
    ) {
      setOrganizationForm((prev: any) => ({ ...prev, search: '' }));
      return;
    }
    const debounceTimer = setTimeout(() => {
      setOrganizationForm((prev: any) => ({
        ...prev,
        search: prev.tempSearch,
      }));
    }, 600);

    return () => clearTimeout(debounceTimer);
  }, [data?.organization?.slug, organizationForm?.tempSearch]);

  useEffect(() => {
    if (!isFetching) {
      setPayload({
        targetHr: data?.target_hr_per_month || 0,
        timezone: data?.timezone,
        name: data?.organization?.name || '',
        billing_info: data?.organization?.billing_info,
        first_name: data?.first_name || '',
        last_name: data?.last_name || '',
        office_title: data?.office_title || '',
        entity: '',
        regNum: '',
        logoFile: null, // Holds the uploaded logo file
        currency_code: data?.organization?.currency_code,
        address: data?.organization?.billing_info?.address,
        country: data?.organization?.billing_info?.country,
        city: data?.organization?.billing_info?.city,
        phone: data?.organization?.billing_info?.phone,
        website: data?.organization?.billing_info?.website,
        state: data?.organization?.billing_info?.state,
        taxes: data?.organization?.taxes?.length
          ? data?.organization?.taxes
          : [],
        contact_stages: data?.organization?.contact_stages?.length
          ? data?.organization?.contact_stages
          : [],
      });
    }
  }, [isFetching]);
  useEffect(() => {
    if (autoSubmit) {
      handleSubmit();
      setAutoSubmit(false);
    }
  }, [autoSubmit]);
  return {
    data,
    isFetching,
    isLoading: isUploading || isLoading || UpdateUserSlugLoading,
    payload,
    isOpen: open,
    isEdit,
    onOpen,
    onClose,
    handleChange,
    handleSubmit,
    setIsEdit,
    setPayload,
    eventForm,
    setEventForm,
    UserBySlugData,
    UserBySlugLoading,
    UserBySlugSuccess,
    setOrganizationForm,
    organizationForm,
    OrganizationBySlugData,
    OrganizationBySlugLoading,
    OrganizationBySlugSuccess,
    dIsLoading,
    taxOption,
    setTaxOption,
    addTax,
    removeTax,
    countryOptions,
    stateOptions,
    cityOptions,
  };
};
export type TUseSlpHook = ReturnType<typeof useSlpHook>;
