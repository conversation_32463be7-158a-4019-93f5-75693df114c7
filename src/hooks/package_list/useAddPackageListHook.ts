import { tableNames } from '@/constants/table_names';
import { useFormik } from 'formik';
import supabase from '@/lib/supabase/client';
import * as Yup from 'yup';
import { useState } from 'react';
import moment from 'moment';
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';
import { useGetAllPackageListApi } from '@/api/package_list/get-all-package_list';
import { useCreatePackageListMutation } from '@/api/package_list/create-package-list';
import { getPrimaryEmail } from '@/utils/helper';

export const useAddPackageListHook = (data?: any, onClose?: any) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const id = data && (data?.id as string);

  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [showClients, setShowClients] = useState(true);
  const [newPackage, setNewPackage] = useState<any>();
  const { mutateAsync: createPackageList } = useCreatePackageListMutation();

  const raw = localStorage.getItem('UserState');
  const orgData = raw ? JSON.parse(raw) : null;
  const org = orgData?.UserState?.organization;
  const { refetch: allPackagesRefetch } = useGetAllPackageListApi(
    org.id
  ) as any;

  interface ProductItem {
    id?: string;
    name: string;
    description: string;
    quantity: number;
    price?: number;
    minutes?: number;
    organization_id?: number;
  }

  const initialValues = {
    products: [] as ProductItem[],
    price: 0,
    status: '',
    expiry_date: moment().format('YYYY-MM-DDTHH:mm'),
    package_name: '',
    amount: 0,
    discount: 0,
    description: '',
    tax: 0,
    expiry: false,
    duration: 0,
    period: '',
  };

  const handleSearchSelect = async (client_id: number) => {
    if (!client_id) return;
    const { data, error } = await supabase
      .from(tableNames.clients)
      .select(`*, client_emails(*)`)
      .eq('id', client_id)
      .single(); // ensures only one row is returned

    if (error) throw error;
    if (!data) return;

    const primaryEmail = getPrimaryEmail(data.client_emails ?? []);

    setNewPackage({
      client_id: data.id,
      email: primaryEmail,
      name: `${data.first_name ?? ''} ${data.last_name ?? ''}`.trim(),
    });

    setShowClients((prev) => !prev);
  };

  const sourceOptions = [
    {
      label: 'E-transfer',
      value: 'E-transfer',
    },
    {
      label: 'Stripe',
      value: 'Stripe',
    },
    {
      label: 'EFT',
      value: 'EFT',
    },
    {
      label: 'Cheque',
      value: 'Cheque',
    },
    {
      label: 'Direct Deposit',
      value: 'Direct-deposit',
    },
    {
      label: 'Comp',
      value: 'Comp',
    },
    {
      label: 'Other',
      value: 'Other',
    },
  ];

  const sessionDuration = [
    {
      label: '30',
      value: '30',
    },
    {
      label: '45',
      value: '45',
    },
    {
      label: '60',
      value: '60',
    },
    {
      label: '90',
      value: '90',
    },
    {
      label: '120',
      value: '120',
    },
  ];

  const statusOptions = [
    {
      label: 'ACTIVE',
      value: 'Active',
    },
    {
      label: 'COMPLETED',
      value: 'COMPLETED',
    },
    {
      label: 'REFUNDED',
      value: 'REFUNDED',
    },
    {
      label: 'PARTIALLY REFUNDED',
      value: 'PARTIALLY REFUNDED',
    },
    // {
    //   label: 'INCOMPLETE',
    //   value: 'INCOMPLETE',
    // },
    {
      label: 'VOIDED',
      value: 'VOIDED',
    },

    // {
    //   label: 'INACTIVE',
    //   value: 'Inactive',
    // },
  ];

  const validationSchema = Yup.object({
    price: Yup.number()
      .typeError('Price must be a number')
      .required('Price is required'),
    amount: Yup.number()
      .typeError('Amount must be a number')
      .required('Amount is required'),
    tax: Yup.number().typeError('Tax must be a number'),
    products: Yup.array()
      .required('Product is required')
      .min(1, 'At least one product is required'),
    package_name: Yup.string().trim(),
    description: Yup.string().trim(),
  });

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
    resetForm,
  } = useFormik({
    initialValues: initialValues,
    validationSchema, // Attach the validation schema
    onSubmit: async (values) => {
      try {
        setLoading(true);

        const expiry_date = values?.expiry
          ? `${values.duration}|${values.period}`
          : null;
        const insert = {
          package_name: values.package_name,
          description: values.description,
          price: values.price,
          amount: values.amount || 0,
          discount: values.discount,
          tax: values.tax,
          expiry: values?.expiry,
          expiry_date,
          organization_id: org?.id,
        };

        if (insert?.package_name) {
          // const { error, data: createdPackage } = await supabase
          //   .from(tableNames.package_list)
          //   .insert(insert)
          //   .select('*')
          //   .single();
          // if (error) throw error;

          // console.log('createdPackage >>>>', createdPackage);
          // const packageProducts = values.products.map((product) => {
          //   return {
          //     ...product,
          //     product_id: product?.id,
          //     package_list_id: createdPackage.id,
          //     organization_id: createdPackage.organization_id,
          //     id: undefined,
          //     description: undefined,
          //     created_at: undefined,
          //     minutes: undefined,
          //     status: undefined,
          //     name: undefined,
          //     price: undefined,
          //   };
          // return supabase.from(tableNames.package_products).insert(newData);
          // });
          // const response = await Promise.all(packageProducts);
          // console.log('response', response);
          await createPackageList({
            data: {
              package: insert,
              packageProducts: values.products,
            },
          });
        } else {
          toaster.create({
            description: 'Something went wrong.',
            type: 'error',
          });
          return;
        }

        if (data) {
          await queryClient.invalidateQueries({
            queryKey: [queryKey.client.getById, id],
          });
        } else {
          await allPackagesRefetch();
        }

        onClose();
        toaster.create({
          description: 'Package Created Successfully',
          type: 'success',
        });
        resetForm(initialValues);
      } catch (error) {
        setLoading(false);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading(false);
      }
    },
  });
  const onProductClick = (product: ProductItem) => {
    // find duplicate product
    const tempProducts = values.products;
    const targetIndex = tempProducts?.findIndex(
      (item) => item?.id === product?.id
    );
    console.log('targetIndex', targetIndex);
    if (targetIndex > -1) {
      const targetProduct = tempProducts[targetIndex];
      tempProducts?.splice(targetIndex, 1, {
        ...targetProduct,
        quantity: targetProduct?.quantity + 1,
      });
    } else {
      tempProducts.push({ ...product, quantity: 1 });
    }
    setFieldValue('products', [...tempProducts]);
    computeTotalPrice(tempProducts);
  };

  const computeTotalPrice = (products: ProductItem[]) => {
    const multiples = products.map((product: ProductItem) => {
      const result = product.price! * product.quantity;
      return result;
    });
    const sum = multiples.reduce((previousValue, currentValue) => {
      const sum = previousValue + currentValue;
      return sum;
    }, 0);
    setFieldValue('price', sum);

    // discount
    const currentAmount = Number(values.amount) || 0;
    if (!values.amount || currentAmount === sum) {
      setFieldValue('amount', sum);
      setFieldValue('discount', '');
    } else {
      const discount = sum - currentAmount;
      setFieldValue('discount', discount > 0 ? discount : '');
    }
  };

  const handleRemoveProduct = (productId: string) => {
    const products = values.products.filter(
      (product) => product.id !== productId
    );
    setFieldValue('products', products);
    computeTotalPrice(products);
  };

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    const products = values.products.map((product) =>
      product.id === productId ? { ...product, quantity: newQuantity } : product
    );
    setFieldValue('products', products);
    computeTotalPrice(products);
  };

  const handleExpiry = () => {
    if (values?.expiry) {
      return setFieldValue('expiry', false);
    }

    // get current date
    // const now = new Date();
    // // pull current year, increase by 1 and set the now to the new date
    // const currentYear = now.getFullYear();
    // const nextYear = currentYear + 1;
    // const oneYearFromNow = now.setFullYear(nextYear);
    // // generate new date and convert to iso readable string
    // const [dateString] = new Date(oneYearFromNow).toISOString().split('T');

    // console.log('dateString', dateString);
    setFieldValue('duration', 1);
    setFieldValue('period', 'YEAR');
    return setFieldValue('expiry', true);
  };

  useEffect(() => {
    if (values.amount < values.price) {
      const discount = values.price - values.amount;
      setFieldValue('discount', discount);
    } else {
      setFieldValue('discount', 0);
    }
  }, [setFieldValue, values.amount, values.price]);

  useEffect(() => {
    if (data) return;
    setValues({
      products: [],
      expiry_date: moment().format('YYYY-MM-DDTHH:mm'),
      price: 0,
      status: '',
      package_name: '',
      amount: 0,
      discount: 0,
      description: '',
      tax: 0,
      expiry: false,
      duration: 0,
      period: '',
    });
  }, [newPackage, setValues, data]);

  return {
    statusOptions,
    handleSubmit,
    sourceOptions,
    loading,
    setShowClients,
    sessionDuration,
    showClients,
    searchResult,
    handleSearchSelect,
    setSearchResult,
    handleChange,
    errors,
    touched,
    values,
    setFieldValue,
    onProductClick,
    handleRemoveProduct,
    handleQuantityChange,
    handleExpiry,
  };
};
