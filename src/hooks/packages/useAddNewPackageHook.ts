import { tableNames } from '@/constants/table_names';
import { useFormik } from 'formik';
import supabase from '@/lib/supabase/client';
import * as Yup from 'yup';
import { useState } from 'react';
import moment from 'moment';
import { useEffect } from 'react';
import { useGetAllPackagesApi } from '@/api/packages/get-all-packages';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';
import { getPrimaryEmail } from '@/utils/helper';

export const useAddNewPackageHook = (data?: any, onClose?: any) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const id = data && (data?.id as string);

  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [showClients, setShowClients] = useState(true);
  const [newPackage, setNewPackage] = useState<any>();
  const { refetch: allPackagesRefetch } = useGetAllPackagesApi() as any;

  const initialValues = {
    product: '',
    balance: '',
    name: newPackage?.name || `${data?.first_name} ${data?.last_name}`,
    price: '',
    status: '',
    expiry_date: moment().format('YYYY-MM-DDTHH:mm'),
    package_size: '',
    source: '',
    // session_quantity: '',
    session_duration: '',
  };

  const handleSearchSelect = async (client_id: number) => {
    if (!client_id) return;

    const { data, error } = await supabase
      .from(tableNames.clients)
      .select(`*, client_emails(*)`)
      .eq('id', client_id)
      .single(); // ensures only one row is returned

    if (error) throw error;
    if (!data) return;

    const primaryEmail = getPrimaryEmail(data.client_emails ?? []);

    setNewPackage({
      client_id: data.id,
      email: primaryEmail,
      name: `${data.first_name ?? ''} ${data.last_name ?? ''}`.trim(),
    });

    setShowClients((prev) => !prev);
  };

  const validationSchema = Yup.object({
    product: Yup.string().trim().required('Product is required'),
    price: Yup.number()
      .typeError('Price must be a number')
      .required('Price is required'),
    balance: Yup.number()
      .typeError('Balance must be a number')
      .moreThan(0, 'Balance cannot be negative'),
    status: Yup.string().trim().required('Status is required'),
    name: Yup.string().trim(),
    package_size: Yup.number()
      .typeError('Package size must be a number')
      .moreThan(0, 'Package cannot be negative'),
    // session_quantity: Yup.number()
    //   .typeError('Quantity must be a number')
    //   .required('Quantity is required'),
    session_duration: Yup.number()
      .typeError('Duration must be a number')
      .required('Duration is required'),
    source: Yup.string().required('Source is required'),
  });

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    setValues,
  } = useFormik({
    initialValues: initialValues,
    validationSchema, // Attach the validation schema
    onSubmit: async (values) => {
      try {
        setLoading(true);

        // Convert expiry_date to moment object for comparison
        // const expiryMoment = moment(values.expiry_date, 'YYYY-MM-DDTHH:mm');
        // const now = moment();

        // if (!expiryMoment.isValid() || expiryMoment.isSameOrBefore(now)) {
        //   toaster.create({
        //     description: 'Expiry date must be in the future.',
        //     type: 'error',
        //   });
        //   setLoading(false);
        //   return;
        // }
        const primaryEmail = getPrimaryEmail(data?.client_emails ?? []);

        const insert = {
          product: values.product,
          total: values.price,
          status: values.status,
          balance: values.balance,
          expiry_date: values.expiry_date,
          package_size: values.package_size,
          source: values.source,
          session_quantity: Number(values.package_size),
          session_duration: Number(values.session_duration),
          name: data
            ? `${data?.first_name} ${data?.last_name}`
            : newPackage?.name,
          client_id: data ? data?.id : newPackage?.client_id,
          email: data ? primaryEmail : newPackage?.email,
          transaction_dt: moment().utc().format('YYYY-MM-DDTHH:mm:ss[Z]'),
        };

        if (insert?.name && insert?.client_id) {
          const { error } = await supabase
            .from(tableNames.packages)
            .insert(insert);
          if (error) throw error;
        } else {
          toaster.create({
            description: 'Something went wrong.',
            type: 'error',
          });
          return;
        }

        if (data) {
          await queryClient.invalidateQueries({
            queryKey: [queryKey.client.getById, id],
          });
        } else {
          await allPackagesRefetch();
        }

        onClose();
        toaster.create({
          description: 'Package Created Successfully',
          type: 'success',
        });
      } catch (error) {
        setLoading(false);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    if (data) return;
    setValues({
      product: '',
      balance: '',
      expiry_date: moment().format('YYYY-MM-DDTHH:mm'),
      // session_quantity: '',
      session_duration: '',
      price: '',
      status: '',
      package_size: '',
      source: '',
      name: newPackage?.name,
    });
  }, [newPackage, setValues, data]);

  return {
    handleSubmit,
    loading,
    setShowClients,
    showClients,
    searchResult,
    handleSearchSelect,
    setSearchResult,
    handleChange,
    errors,
    touched,
    values,
    setFieldValue,
  };
};
