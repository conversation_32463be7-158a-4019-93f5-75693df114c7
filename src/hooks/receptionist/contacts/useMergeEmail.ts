import { findEmailByClientId } from '@/api/client_emails';
import { GetSingleClientHookReturnType } from '@/app/(dashboard)/contacts/[id]/profile/email';
import { tableNames } from '@/constants/table_names';
import { useState } from 'react';
import supabase from '@/lib/supabase/client';
import { ToastMessages } from '@/constants/toast-messages';
import { FullClient } from '@/shared/interface/clients';
import { createNewClientEmails } from '@/utils/db';
import { useDisclosure } from '@chakra-ui/react';
import { toaster } from '@/components/ui/toaster';
import { getPrimaryEmail } from '@/utils/helper';

export const useMergeEmail = ({
  getClientHook,
  onClose,
  data,
}: {
  getClientHook: GetSingleClientHookReturnType;
  onClose: any;
  data: FullClient;
}) => {
  const [lookupEmail, setLookupEmail] = useState('');
  const [lookupEmailLoading, setLookupEmailLoading] = useState(false);
  const [confirmMerge, setConfirmMerge] = useState(false);
  const [mergeData, setMergeData] = useState<any>();

  const [isNewEmail, setIsNewEmail] = useState(false);

  const [mergingEmailLoading, setMergingEmailLoading] = useState(false);
  const dialogDisclosure = useDisclosure();

  const isNewEmailText =
    'No email found, would you like to create a new email for this user?';

  const returnClientEmailsFull = async (email: string) => {
    const { data } = await supabase
      .from(tableNames.client_emails)
      .select(`id, email, clients(*)`)
      .limit(1)
      .eq('email', email);
    return data;
  };
  const onInputChange = (e: any) => {
    setLookupEmail(e.target.value);
    setConfirmMerge(false);
    setMergeData(null);
  };

  const lookupEmailFunc = async () => {
    try {
      setLookupEmailLoading(true);
      const email = lookupEmail.trim();
      if (email.length > 5) {
        const clientData = await returnClientEmailsFull(email);

        if (clientData && clientData.length > 0) {
          // TODO: Set specific parameters that are needed

          setMergeData(clientData[0]);
          setConfirmMerge(true);
        } else {
          // if email does not exist, notify client if they want to merge email
          setConfirmMerge(true);
          setIsNewEmail(true);
        }
      } else {
        toaster.create({
          description: 'Please input valid email',
          type: 'error',
        });
      }
    } catch (error) {
      toaster.create({
        description: ToastMessages.somethingWrong,
        type: 'error',
      });
    } finally {
      setLookupEmailLoading(false);
    }
  };

  // =========================================
  // Updates the client email table, change searched client's corresponding to the current client id
  const updateClientEmails = async () => {
    try {
      const id = mergeData?.clients?.id;

      // Fetch current client emails
      const currentClientEmails = await findEmailByClientId(data.id);

      if (!currentClientEmails) {
        throw new Error('Unable to fetch client emails.');
      }

      // Determine if there's already a primary email
      const hasPrimaryEmail = currentClientEmails.find(
        (item) => item.is_primary_email === true
      );

      // Update client emails for existing emails
      const { error } = await supabase
        .from(tableNames.client_emails)
        .update({
          client_id: data.id,
          is_primary_email: hasPrimaryEmail ? false : true, // True if no primary email exists
        })
        .eq('client_id', id);

      console.log('error--love', error);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating client emails:', error);
      throw error;
    }
  };
  // const updateClientEmails = async (client_id?: any) => {
  //   const id = mergeData?.clients?.id || client_id;

  //   const currentClientEmails = await findEmailByClientId(data.id);
  //   const hasPrimaryEmail = currentClientEmails?.find(
  //     (item) => item.is_primary_email === true
  //   );

  //   const { error } = await supabase
  //     .from(tableNames.client_emails)
  //     .update({
  //       client_id: data.id,
  //       is_primary_email: hasPrimaryEmail ? false : true,
  //     })
  //     .eq('client_id', id);
  //   // return;

  //   if (error) throw error;
  // };

  // Updates the client id of the client activities
  const updateClientActivities = async () => {
    const { error } = await supabase
      .from(tableNames.client_activities)
      .update({ client_id: data.id })
      .eq('client_id', mergeData.clients.id);
    if (error) throw error;
  };

  // test test test

  //Updates the client id of the booking
  const updateBooking = async () => {
    const { error } = await supabase
      .from(tableNames.bookings)
      .update({ client_id: data.id })
      .eq('client_id', mergeData.clients.id);

    if (error) throw error;
  };

  //Updates the client id of the invoice if the merged data email matches
  const updateInvoices = async () => {
    const { error } = await supabase
      .from(tableNames.invoices)
      .update({ client_id: data.id })
      .eq('client_id', mergeData.clients.id);
    if (error) throw error;
  };

  //Updates the client id of the invoice if the merged data email matches
  const updatePackages = async () => {
    const { error } = await supabase
      .from(tableNames.packages)
      .update({ client_id: data.id })
      .eq('client_id', mergeData.clients.id);
    if (error) throw error;
  };

  //Updates the client id of the invoice if the merged data email matches
  const updateFollowUp = async () => {
    const { error } = await supabase
      .from(tableNames.followups)
      .update({ client_id: data.id })
      .eq('client_id', mergeData.clients.id);
    if (error) throw error;
  };

  //Updates the client id of the invoice if the merged data email matches
  const updateReferrer = async () => {
    const { error } = await supabase
      .from(tableNames.referrals)
      .update({ referrer_id: data.id })
      .eq('referrer_id', mergeData.clients.id);
    if (error) throw error;
  };

  const updateReferee = async () => {
    const { error } = await supabase
      .from(tableNames.referrals)
      .update({ referee_id: data.id })
      .eq('referee_id', mergeData.clients.id);
    if (error) throw error;
  };

  //Updating linked client
  const updateLinkedClient = async () => {
    const { error } = await supabase
      .from(tableNames.linked_clients)
      .update({ client_id: data.id })
      .eq('client_id', mergeData.clients.id);
    if (error) throw error;
  };

  //Updating soap note
  const updateSlpNote = async () => {
    const { error } = await supabase
      .from(tableNames.slp_notes)
      .update({ client_id: data.id })
      .eq('client_id', mergeData.clients.id);
    if (error) throw error;
  };

  // Update client to new client
  const mergeClient = async (newClient: any) => {
    const { error } = await supabase
      .from(tableNames.clients)
      .update(newClient)
      .eq('id', data.id);

    if (error) throw error;
  };

  // Delete old client
  const deleteOldClient = async () => {
    const { error } = await supabase
      .from(tableNames.clients)
      .delete()
      .eq('id', mergeData.clients.id);

    if (error) throw error;
  };

  const mergeClients = async () => {
    try {
      setMergingEmailLoading(true);
      const primaryEmail = getPrimaryEmail(data?.client_emails);

      // condition to prevent merging  with self
      if (primaryEmail === mergeData?.email) {
        toaster.create({
          description: 'Cannot merge with this profile',
          type: 'error',
        });

        return;
      }

      // if email does not exist create a new email
      if (isNewEmail) {
        const newClientEmail = await createNewClientEmails(
          data.id,
          lookupEmail.trim().toLowerCase(),
          data?.organization_id
        );

        console.log('newClientEmail', newClientEmail);

        if (newClientEmail) {
          // await updateClientEmails(
          //   newClientEmail?.client_id,
          //   newClientEmail?.id
          // );

          toaster.create({
            description: 'Email merged successfully.',
            type: 'success',
          });
          await getClientHook.refetch();

          onClose();
          dialogDisclosure.onClose();
        }
      } else {
        // Create new client object

        const newClient = mergeData ? { ...mergeData.clients } : {};

        // newClient.first_name ??= data.first_name;
        // newClient.last_name ??= data.last_name;
        // newClient.notes ??= '';
        // newClient.notes += data.notes ?? '';
        // newClient.province ??= data.province;
        // newClient.phone ??= data.phone;
        // newClient.utm_campaign ??= data.utm_campaign;
        // newClient.utm_content ??= data.utm_content;
        // newClient.utm_medium ??= data.utm_medium;
        // newClient.utm_source ??= data.utm_source;
        // newClient.email = data.email;
        // delete newClient.id;

        newClient.first_name = data.first_name || newClient.first_name;
        newClient.last_name = data.last_name || newClient.last_name;
        newClient.display_name = data.display_name || newClient.display_name;
        // newClient.notes = data.notes || '';
        // newClient.notes += data.notes ?? '';
        newClient.notes = (newClient.notes ?? '') + (data.notes ?? '');

        newClient.province = data.province || newClient.province;
        newClient.phone = data.phone || newClient.phone;
        newClient.utm_campaign = data.utm_campaign || newClient.utm_campaign;
        newClient.utm_content = data.utm_content || newClient.utm_content;
        newClient.utm_medium = data.utm_medium || newClient.utm_medium;
        newClient.utm_source = data.utm_source || newClient.utm_source;
        //newClient.email = data.email;
        newClient.lead_quality = data.lead_quality || newClient.lead_quality;
        newClient.active_slp = data?.active_slp?.id || newClient.active_slp;
        newClient.stage = data.stage || newClient.stage;
        newClient.goals = data.goals || newClient.goals;
        newClient.referral_source =
          data.referral_source || newClient.referral_source;

        delete newClient.id;

        // if its  a new email for the the same client just merge the emails
        await updateClientEmails();
        await updateClientActivities();
        // test
        await updateBooking();
        await updateInvoices();
        await updatePackages();
        await updateFollowUp();
        await updateLinkedClient();
        await updateSlpNote();
        await updateReferee();
        await updateReferrer();
        await mergeClient(newClient);
        await deleteOldClient();
        await getClientHook.refetch();

        toaster.create({
          description: 'Client merged successfully.',
          type: 'success',
        });

        onClose();
        dialogDisclosure.onClose();
      }
    } catch (error: any) {
      dialogDisclosure.onClose();
      toaster.create({
        description: ToastMessages.somethingWrong,
        type: 'error',
      });
    } finally {
      dialogDisclosure.onClose();
      setMergingEmailLoading(false);
    }

    // alert('Updated');
    // setOpen(false);
    // setForm('');
  };

  // ========================

  return {
    lookupEmail,
    setLookupEmail,
    lookupEmailFunc,
    lookupEmailLoading,
    setConfirmMerge,
    mergeData,
    confirmMerge,
    mergeClients,
    isNewEmail,
    isNewEmailText,
    mergingEmailLoading,
    onInputChange,
    dialogDisclosure,
  };
};
