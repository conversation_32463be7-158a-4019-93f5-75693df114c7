import { useFormik } from 'formik';
import { useCallback, useEffect, useState } from 'react';
import { useUpdateClientMutation } from '@/api/clients/update-client';
import { FullClient } from '@/shared/interface/clients';
import { ToastMessages } from '@/constants/toast-messages';
import { updateIncompleteFollowUp } from '@/api/follow_up';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { toaster } from '@/components/ui/toaster';
import { useContactStages } from '@/hooks/clients/useGetContactsStages';

export const useUpdateClientHook = ({
  contact,
  setShowEditClient,
}: {
  contact: FullClient;
  setShowEditClient: any;
}) => {
  const { mutateAsync: UpdateClient } = useUpdateClientMutation();
  const queryClient = useQueryClient();
  const { contactStagesOptions } = useContactStages(false);

  const [updateLoading, setUploadLoading] = useState(false);
  const updateClientInitialValues = {
    ...contact,
    goals: contact?.goals ?? [],
  } as any;
  const [isUserEditingDisplayName, setIsUserEditingDisplayName] =
    useState('rendered');
  const [displayNameError, setDisplayNameError] = useState(false);
  const [error, setError] = useState(false);

  const {
    values,
    handleSubmit,
    errors,
    handleChange,
    touched,
    handleBlur,
    setFieldValue,
  } = useFormik({
    initialValues: updateClientInitialValues,
    enableReinitialize: true,

    onSubmit: async (values) => {
      const {
        first_name,
        last_name,
        middle_name,
        phone,
        active_slp,
        province,
        display_name,
        notes,
        stage,
        lead_quality,
        goals,
        referral_source,
        waitlist,
        country,
        state,
        city,
        address,
        postal_code,
        dob,
      } = values;
      // console.log('values is ', values);
      // console.log('contact is ', contact);

      // return;
      try {
        setUploadLoading(true);
        if (displayNameError) {
          setError((prev) => !prev);
          return;
        }

        const payload = {
          first_name,
          last_name,
          middle_name,
          phone,
          display_name,
          notes,
          stage,
          lead_quality,
          goals,
          waitlist,
          province: province ?? 'Other',
          referral_source: referral_source ?? 'Unknown',
          active_slp: active_slp?.id ? active_slp?.id : active_slp,
          country,
          state,
          city,
          address,
          postal_code,
          dob,
        };

        await UpdateClient({
          data: payload as any,
          initialData: contact,
          id: contact.id,
        });

        // Check if contact.stage is "Completed" or "Unqualified"
        if (contact.stage === 'Customer' || contact.stage === 'Unqualified') {
          // Remove corresponding entry from the followups table
          await updateIncompleteFollowUp({ status: 'Completed' }, contact.id);
        }

        await queryClient.refetchQueries({
          queryKey: [queryKey.client.getActivities, contact.id],
        });

        await queryClient.invalidateQueries({
          queryKey: [queryKey.client.getById, Number(contact.id)],
        });

        toaster.create({ type: 'success', description: 'Profile Updated' });
        setShowEditClient(false);
      } catch (e: any) {
        toaster.create({
          description: e.message || ToastMessages.somethingWrong,
          type: 'error',
        });
      } finally {
        setUploadLoading(false);
      }
    },
  });

  const submitForm = () => {
    handleSubmit();
  };

  const validateSelection = useCallback(
    (value: string) => {
      const [firstName, lastName] = value.trim().split(' ');
      if (firstName && lastName) setError(false);
      return !(firstName && lastName);
    },
    [setError]
  );

  // Function to handle changes in the display name field
  const handleDisplayNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('true');
    setFieldValue('display_name', value);
    setDisplayNameError(validateSelection(value));
  };
  const handleFirstnameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('false');
    setFieldValue('first_name', value);
  };
  const handleLastnameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setIsUserEditingDisplayName('false');
    setFieldValue('last_name', value);
  };

  useEffect(() => {
    if (isUserEditingDisplayName === 'false') {
      const firstName = values.first_name?.trim() || '';
      const lastName = values.last_name?.trim() || '';

      const autoGeneratedDisplayName = `${firstName} ${lastName}`.trim();

      if (autoGeneratedDisplayName) {
        setFieldValue('display_name', autoGeneratedDisplayName);
        setDisplayNameError(validateSelection(autoGeneratedDisplayName));
      }
    }
    if (isUserEditingDisplayName === 'rendered') {
      if (values.display_name) {
        setFieldValue('display_name', values.display_name);
      } else {
        const firstName = values.first_name?.trim() || '';
        const lastName = values.last_name?.trim() || '';
        const autoGeneratedDisplayName = `${firstName} ${lastName}`.trim();

        if (autoGeneratedDisplayName) {
          setFieldValue('display_name', autoGeneratedDisplayName);
          setDisplayNameError(validateSelection(autoGeneratedDisplayName));
        }
      }
    }
  }, [
    values.first_name,
    validateSelection,
    values.last_name,
    isUserEditingDisplayName,
    setFieldValue,
    values.display_name,
    setDisplayNameError,
  ]);

  useEffect(() => {
    if (values.display_name === '' && isUserEditingDisplayName === 'false') {
      const firstName = values.first_name?.trim() || '';
      const lastName = values.last_name?.trim() || '';

      const autoGeneratedDisplayName = `${firstName} ${lastName}`.trim();

      if (autoGeneratedDisplayName) {
        setFieldValue('display_name', autoGeneratedDisplayName);
        setDisplayNameError(validateSelection(autoGeneratedDisplayName));
      }
    }
  }, [
    values.display_name,
    values.first_name,
    values.last_name,
    setFieldValue,
    setDisplayNameError,
    isUserEditingDisplayName,
    validateSelection,
  ]);
  return {
    values,
    submitForm,
    errors,
    handleChange,
    contactStagesOptions,
    handleDisplayNameChange,
    handleFirstnameChange,
    handleLastnameChange,
    setIsUserEditingDisplayName,
    touched,
    error,
    setError,
    setDisplayNameError,
    handleBlur,
    setFieldValue,
    updateLoading,
  };
};
