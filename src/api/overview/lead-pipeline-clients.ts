import { queryKey } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';

type Client = {
  client_id: number;
  display_name: string;
  initial_email: string;
  phone: string;
  stage: string;
};

type Params = {
  stage: string;
  since_date: string;
};

async function getLeadPipelineClients(params: Params): Promise<Client[]> {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const url = new URL(
    '/api/overview/lead-pipeline-clients',
    window.location.origin
  );

  if (!org?.id) {
    throw new Error('Missing organization ID');
  }

  url.searchParams.set('org_id', org?.id.toString());
  url.searchParams.set('stage', params.stage);
  url.searchParams.set('since_date', params.since_date);

  const response = await fetch(url.toString(), {
    method: 'GET',
  });

  const json = await response.json();

  if (!response.ok) {
    throw new Error(json.message || 'Failed to fetch lead pipeline clients');
  }

  return json;
}

type QueryFnType = typeof getLeadPipelineClients;

export const useGetLeadPipelineClientsQuery = (
  params: Params,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    queryKey: [queryKey.overview.leadPipelineClients, params],
    queryFn: () => getLeadPipelineClients(params),
    ...config,
  });
};
