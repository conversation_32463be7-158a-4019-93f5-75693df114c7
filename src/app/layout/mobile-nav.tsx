'use client';

import { useAuth } from '@/hooks/auth/useAuth';
import {
  Flex,
  IconButton,
  Box,
  Text,
  Input,
  Stack,
  Spinner,
  Icon,
  Separator,
  Container,
  useDisclosure,
  DrawerBackdrop,
  DrawerBody,
  DrawerCloseTrigger,
  Drawer<PERSON>ontent,
  DrawerRoot,
  HStack,
  Link,
} from '@chakra-ui/react';
import { useGetSearchClientsHook } from '@/hooks/receptionist/contacts/useGetSearchClientsHook';
import { useEffect, useState, useRef, useCallback } from 'react';
import { FiMenu } from 'react-icons/fi';
import NextLink from 'next/link';
import { LuLogOut, LuSearch, LuUser } from 'react-icons/lu';
import { InputGroup } from '../../components/ui/input-group';
import {
  MenuRoot,
  MenuContent,
  MenuSeparator,
  MenuTrigger,
  MenuItem,
} from '../../components/ui/menu';
// import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { getPrimaryEmail } from '@/utils/helper';
import SidebarContent from './sidebar';
import userPlaceholderImage from '@/assets/user-image.png';
import { Image } from '@chakra-ui/react';
import { useGetUserByEmailQuery } from '@/api/users/get-user-by-email';
import NotificationBell from '@/components/ui/notification';
import { IoMdTime } from 'react-icons/io';
import { useRecentSearches } from '@/hooks/common/useRecentSearches';
import StagesContainer from '@/components/elements/StagesContainer';

export default function MobileNav({ userFromServer }: any) {
  const { onOpen, onClose, open } = useDisclosure();
  const [showDropdown, setShowDropdown] = useState(false);
  const { recentSearches, addClientToRecent } = useRecentSearches();

  const searchContainerRef = useRef<HTMLDivElement>(null);

  // console.log('This is path name', pathName);

  const { handleSignOut } = useAuth();
  const [renderKey, setRenderKey] = useState(0);
  const getClientHook = useGetSearchClientsHook();
  const { data: userData } = useGetUserByEmailQuery(userFromServer?.email, {
    enabled: Boolean(userFromServer?.email),
  });

  // const { UserFromQuery } = useSupabaseSession();

  const { isPending, handleInputChange, search, setSearch, data } =
    getClientHook;

  const clearSearch = useCallback(() => {
    setSearch('');
    setShowDropdown(false);
    setRenderKey((prev) => prev + 1);
  }, [setSearch, setRenderKey]);

  // Show dropdown on input focus
  const handleInputFocus = () => {
    setShowDropdown(true);
  };

  // Essential for click outside detection
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        clearSearch();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [clearSearch]);

  // For escape key support
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        clearSearch();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [clearSearch]);

  return (
    <Box
      zIndex={100}
      position={'fixed'}
      top={0}
      m={'auto'}
      right={'0'}
      left={{ base: '0', lg: '250px' }}
    >
      <Box display={{ base: 'block', lg: 'none' }}>
        <DrawerRoot open={open} onOpenChange={onClose} placement={'start'}>
          <DrawerBackdrop />
          <DrawerContent pos={'absolute'} minH={'100vh'}>
            <DrawerBody pl={'0'} scrollbar={'hidden'} scrollBehavior="smooth">
              <SidebarContent
                userFromServer={userFromServer}
                onClose={onClose}
              />
            </DrawerBody>
            <DrawerCloseTrigger />
          </DrawerContent>
        </DrawerRoot>
      </Box>
      <Box borderBottom={'1px solid rgba(0,0,0,0.2)'}>
        <Container maxW="98%" px={{ base: '1', md: '4' }}>
          <Flex
            position="relative"
            height={{ base: '4.5rem', md: '5.051rem' }}
            alignItems="center"
            bg={'white'}
            justifyContent={'space-between'}
            gap={{ base: '1', md: '2' }}
            px={{ base: '10px', md: '0' }}
          >
            <IconButton
              aria-label="Mobile hamburger"
              onClick={onOpen}
              variant="outline"
              hideFrom={'lg'}
              w={'5'}
              mr={'2'}
            >
              <FiMenu />
            </IconButton>
            <Box
              ref={searchContainerRef}
              position="relative"
              maxW={'60%'}
              w={'full'}
            >
              <InputGroup
                maxWidth={'50%'}
                width={'full'}
                mr={'2'}
                alignSelf="stretch"
                startElement={
                  // <Icon size="lg">
                  //   <LuSearch />
                  // </Icon>
                  <Icon as={LuSearch} />
                }
              >
                <Input
                  bg="bg"
                  ps="12"
                  rounded="lg"
                  placeholder="Search for..."
                  // size="2xl"
                  focusRingColor="transparent"
                  _focusVisible={{ shadow: 'lg' }}
                  value={search}
                  onChange={handleInputChange}
                  onFocus={handleInputFocus}
                />
              </InputGroup>
              {showDropdown &&
                search?.length === 0 &&
                recentSearches?.some((obj) => obj && obj.id) && (
                  <Stack
                    position="absolute"
                    top="100%"
                    w={'fit'}
                    minW={{ base: '90%', md: '60%', lg: '70%' }}
                    //width={{ base: '90%', md: '60%' }}
                    maxHeight="330px"
                    overflowY={'auto'}
                    overflowX={'hidden'}
                    border="1px solid"
                    borderColor={'gray.50'}
                    borderRadius="md"
                    padding="0.5rem"
                    boxShadow="md"
                    py={'5'}
                    px={'3'}
                    bg="white"
                    zIndex={999}
                    key={renderKey}
                  >
                    <HStack px={'1.5'} color={'gray.400'}>
                      <IoMdTime size={18} />
                      <Text fontWeight={'500'} mt={'1'} fontSize={'md'}>
                        Recent Searches
                      </Text>
                    </HStack>

                    {recentSearches
                      .filter((obj) => obj && Object.keys(obj).length > 0)
                      .map((term, idx) => (
                        <Link
                          href={`/contacts/${term.id}`}
                          onClick={() => clearSearch()}
                          className="btnLink"
                          key={
                            term.id + term.stage + term.display_name + '' + idx
                          }
                          as={NextLink}
                        >
                          <Box
                            css={{
                              w: 'full',
                              whiteSpace: 'nowrap',
                              display: 'block',
                              padding: '.5rem',
                              transition:
                                'border 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                              border: '1px solid transparent',
                              borderRadius: 'md',
                              _hover: {
                                border: '1px solid',
                                borderColor: 'gray.200',
                                boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
                              },
                            }}
                          >
                            <Flex
                              alignItems={'center'}
                              justifyContent={'space-between'}
                              gapX={6}
                            >
                              <Flex
                                flexWrap={{ base: 'wrap', md: 'nowrap' }}
                                alignItems={'flex-end'}
                                gap={'.4rem'}
                              >
                                <Text fontWeight={600}>
                                  {term?.display_name}
                                </Text>
                                {term?.primary_email?.trim() && (
                                  <>
                                    <Separator
                                      orientation="vertical"
                                      height="20px"
                                      mx={1}
                                    />
                                    <Text>{term?.primary_email}</Text>
                                  </>
                                )}
                              </Flex>
                              {term?.stage && (
                                <StagesContainer stageName={term?.stage} />
                              )}
                            </Flex>
                          </Box>
                        </Link>
                      ))}
                  </Stack>
                )}

              {showDropdown && search.length > 0 && (
                <Stack
                  position="absolute"
                  top="100%"
                  width={{ base: '90%', md: '60%' }}
                  gap={'1rem'}
                  maxHeight="400px"
                  overflowY="auto"
                  border="1px solid"
                  borderColor={'gray.50'}
                  borderRadius="md"
                  padding="0.5rem"
                  boxShadow="md"
                  py={'5'}
                  px={'3'}
                  bg="white"
                  zIndex={999}
                  key={renderKey}
                >
                  {isPending ? (
                    <Flex
                      justifyContent="center"
                      alignItems="center"
                      height="100%"
                    >
                      <Spinner size="md" />
                    </Flex>
                  ) : data?.data?.length > 0 ? (
                    <>
                      {data?.data?.map((item: any, idx: number) => {
                        const primaryEmail = getPrimaryEmail(item?.emails);
                        return (
                          <Link
                            href={'/contacts/' + item.id}
                            className="btnLink"
                            // passHref
                            key={
                              item.id + item.stage + item.last_name + '' + idx
                            }
                            as={NextLink}
                          >
                            <Box
                              css={{
                                w: 'full',
                                display: 'block',
                                padding: '.5rem',
                                transition:
                                  'border 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
                                border: '1px solid transparent',
                                borderRadius: 'md',
                                _hover: {
                                  border: '1px solid',
                                  borderColor: 'gray.200',
                                  boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
                                },
                              }}
                              onClick={() => {
                                clearSearch();
                                addClientToRecent({
                                  id: item.id,
                                  display_name:
                                    item.display_name ||
                                    `${item.first_name} ${item.last_name}`,
                                  stage: item.stage,
                                  primary_email: primaryEmail,
                                });
                              }}
                            >
                              <Flex
                                alignItems={'center'}
                                justifyContent={'space-between'}
                              >
                                <Flex
                                  flexWrap={'wrap'}
                                  alignItems={'flex-end'}
                                  gap={'.4rem'}
                                >
                                  <Text fontWeight={600}>
                                    {item.display_name ||
                                      `${item.first_name} ${item.last_name}`}
                                  </Text>
                                  <Separator
                                    orientation="vertical"
                                    height="20px"
                                    mx={1}
                                  />
                                  <Text>{primaryEmail}</Text>
                                </Flex>
                                <StagesContainer stageName={item?.stage} />
                              </Flex>
                            </Box>
                          </Link>
                        );
                      })}
                    </>
                  ) : (
                    <Box textAlign="center" color="gray.500">
                      No results found.
                    </Box>
                  )}
                </Stack>
              )}
            </Box>

            <Box
              gap={'1rem'}
              alignItems={'center'}
              // minW={{ md: '215px' }}
              // maxW={{ md: '215px' }}
              // w={'full'}
              display={'flex'}
            >
              <NotificationBell userFromServer={userFromServer} />

              <MenuRoot positioning={{ placement: 'bottom' }}>
                <MenuTrigger
                  cursor={'pointer'}
                  css={{ minWidth: 'auto' }}
                  border={'none !important'}
                  textDecor={'none !important'}
                  boxShadow={'none'}
                  outline={'none'}
                  w={'full'}
                >
                  <Stack
                    alignItems={'flex-start'}
                    minW={{ base: 'auto', md: '100px' }}
                    // border={'none !important'}
                    // textDecor={'none !important'}
                    // boxShadow={'none'}
                    // outline={'none'}
                  >
                    <HStack>
                      <Image
                        src={userPlaceholderImage.src}
                        alt="logo"
                        w={'50px'}
                        objectFit={'contain'}
                      />
                      <Stack alignItems={'flex-start'} w={'full'}>
                        <Text
                          fontSize="sm"
                          fontWeight={'600'}
                          lineClamp={1}
                          hideBelow={'md'}
                          w={'full'}
                          textAlign={'left'}
                        >
                          {userFromServer?.first_name}{' '}
                          {userFromServer?.last_name}
                        </Text>
                        <Text fontSize="sm" lineClamp={1} hideBelow={'md'}>
                          {(
                            userData?.office_title ||
                            userFromServer?.office_title
                          )?.replace(/_/g, ' ')}
                        </Text>
                      </Stack>
                    </HStack>
                    {/* <Icon hideFrom={'md'} size={'lg'}>
                    <LuUserRound />
                  </Icon> */}
                  </Stack>
                </MenuTrigger>
                <MenuContent cursor={'pointer'}>
                  <Link
                    w={'100%'}
                    border={'none !important'}
                    textDecor={'none !important'}
                    boxShadow={'none'}
                    outline={'none'}
                    as={NextLink}
                    href={'/profile'}
                  >
                    <MenuItem
                      border="none !important"
                      outline="none !important"
                      value="profile"
                    >
                      <LuUser />
                      Profile
                    </MenuItem>
                  </Link>
                  <MenuSeparator />
                  <MenuItem value="logout" onClick={handleSignOut}>
                    <LuLogOut />
                    Logout
                  </MenuItem>
                </MenuContent>
              </MenuRoot>
            </Box>
          </Flex>
        </Container>
      </Box>
    </Box>
  );
}
