import {
  createBaseInvoice,
  getMaxInvoiceNumber,
  updateInvoiceById,
} from '@/app/service/invoice';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getNumberParam } from '@/utils/format-object';
import { NextResponse } from 'next/server';
import { handleInvoiceItems } from './util';
import { updateClientById } from '@/app/service/client';

import { KlaviyoActions } from '@/constants/klaviyo-actions';
import { dispatchKlaviyoEvent } from '@/lib/klaviyo/service';

export async function POST(request: Request) {
  try {
    // const supabase = getSupabaseClient(request);
    const supabase = createSupabaseServer();
    const body = await request.json();

    // console.log('body is ', body);

    const {
      currency_code,
      email,
      name,
      client_id,
      invoice_date,
      due_date,
      items,
      organization_id,
      user_id,
      tax_value,
      total_price,
      slp,
      discount,
      memo,
      event,
    } = body;

    console.log('event is ', JSON.stringify(event, null, 2));

    // return;

    if (
      !currency_code ||
      !name ||
      !client_id ||
      !invoice_date ||
      !organization_id ||
      !user_id
    ) {
      return NextResponse.json(
        { success: false, message: 'Missing required invoice fields.' },
        { status: 400 }
      );
    }

    // 1. Generate a new invoice number
    const currentInvoiceNumber = await getMaxInvoiceNumber(
      organization_id,
      supabase
    );
    const newInvoiceNumber = currentInvoiceNumber + 1;

    // 2. Create the base invoice record
    const baseInvoice = await createBaseInvoice(
      {
        email,
        invoice_number: newInvoiceNumber,
        name,
        invoice_date_raw: invoice_date,
        client_id,
        invoice_date: new Date(invoice_date),
        due_date: new Date(due_date),
        status: 'AWAITING_PAYMENT',
        slp_id: user_id,
        data_source: 'manual',
        organization_id,
        currency_code,
        tax_value,
        total_price,
        qty: items?.length,
        slp,
        discount,
        memo,
      },
      supabase
    );

    // 3. Add line items and calculate total
    const invoiceTotal = await handleInvoiceItems(
      baseInvoice.id,
      items,
      supabase,
      { user_id, organization_id, client_id }
    );

    // 4. Update the invoice with the total price
    const updatedInvoice = await updateInvoiceById(
      baseInvoice.id,
      { total_price: invoiceTotal },
      supabase
    );
    // 4. Update the client with the total price
    await updateClientById(client_id, { stage: 'Customer' }, supabase);

    //5 Create Klaviyo Event
    if (event) {
      await dispatchKlaviyoEvent(
        organization_id,
        supabase,
        event,
        KlaviyoActions.INVOICE_CREATED
      );
    }
    return NextResponse.json({ success: true, data: updatedInvoice });
  } catch (error: any) {
    console.log(
      'Error in POST /api/%28package-structure%29/newsf/invoice/route.ts:',
      error
    );
    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const supabase = createSupabaseServer();

  const org_id = searchParams.get('organization_id');
  const search = searchParams.get('search');
  // if (!org_id) {
  //   return NextResponse.json(
  //     { error: 'Organization ID is required' },
  //     { status: 400 }
  //   );
  // }

  // Pagination params with validation
  const currentPage = getNumberParam(searchParams, 'page_number', 1);
  const itemsPerPage = getNumberParam(searchParams, 'items_per_page', 50);

  // Validate pagination bounds
  if (currentPage < 1) {
    return NextResponse.json(
      { error: 'page_number must be greater than 0' },
      { status: 400 }
    );
  }

  if (itemsPerPage < 1 || itemsPerPage > 100) {
    return NextResponse.json(
      { error: 'items_per_page must be between 1 and 100' },
      { status: 400 }
    );
  }

  // Optional filters
  const client_id = searchParams.get('client_id');
  const status = searchParams.get('status');
  const slp_id = searchParams.get('slp_id');

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage - 1;

  let query = supabase.from(tableNames.invoices).select(
    `
       *,
    client:clients (*, client_emails (*)),
    transactions!transactions_invoice_id_fkey (*),
    invoice_items (
      *,
      package_offering (*, package_items (services (*))),
      services (*),
      taxes (*)
    )
        `,
    { count: 'exact' }
  );
  // .eq('organization_id', org_id);

  // Apply filters
  if (client_id) {
    query = query.eq('client_id', Number(client_id));
  }
  if (org_id) {
    query = query.eq('organization_id', Number(org_id));
  }

  if (status) {
    query = query.eq('status', status);
  }

  if (slp_id) {
    query = query.eq('slp_id', Number(slp_id));
  }

  // Apply search filter
  if (search && search.trim()) {
    const searchTerm = search.trim();
    query = query.or(
      `name.ilike.%${searchTerm}%,slp.ilike.%${searchTerm}%,invoice_number.ilike.%${searchTerm}%`
    );
  }

  // Execute query with pagination and ordering
  const {
    data: invoices,
    error: queryError,
    count,
  } = await query
    .order('created_dt', { ascending: false })
    .range(startIndex, endIndex);

  if (queryError) {
    console.error('Supabase query error:', queryError);
    return NextResponse.json(
      {
        error: 'Failed to fetch invoices',
        code: queryError.code,
      },
      { status: 500 }
    );
  }

  // Handle empty results
  if (!invoices) {
    return NextResponse.json({
      data: [],
      pagination: {
        page_number: currentPage,
        total_count: 0,
        items_per_page: itemsPerPage,
      },
    });
  }
  const updatedInvoices = invoices.map((invoice: any) => {
    let amountDue = 0;

    // Calculate discount amount if discount exists
    let discountAmount = 0;
    if (invoice.discount && typeof invoice.discount === 'object') {
      if (invoice.discount.value > 0) {
        if (invoice.discount.type === 'percentage') {
          discountAmount = (invoice.total_price * invoice.discount.value) / 100;
        } else {
          discountAmount = invoice.discount.value;
        }
      }
    } else if (typeof invoice.discount === 'number' && invoice.discount > 0) {
      discountAmount = invoice.discount;
    }

    if (invoice.transactions && invoice.transactions.length > 0) {
      const totalPaid = invoice.transactions.reduce(
        (sum: number, transaction: any) =>
          sum + Number(transaction.amount || 0),
        0
      );
      amountDue = Number(invoice.total_price) - discountAmount - totalPaid;
    } else {
      amountDue = Number(invoice.total_price) - discountAmount;
    }

    // Calculate total_duration_minutes for this invoice
    let totalDuration = 0;
    for (const item of invoice.invoice_items || []) {
      if (item.services && typeof item.services.duration_minutes === 'number') {
        totalDuration += item.services.duration_minutes;
      } else if (
        item.package_offering &&
        Array.isArray(item.package_offering.package_items)
      ) {
        for (const pkgItem of item.package_offering.package_items) {
          if (
            pkgItem.services &&
            typeof pkgItem.services.duration_minutes === 'number'
          ) {
            totalDuration += pkgItem.services.duration_minutes;
          }
        }
      }
    }

    return {
      ...invoice,
      amount_due: amountDue,
      total_duration_minutes: totalDuration, // <-- Add this property
    };
  });

  return NextResponse.json({
    data: updatedInvoices,
    pagination: {
      page_number: currentPage,
      total_count: count || 0,
      items_per_page: itemsPerPage,
    },
  });
}
