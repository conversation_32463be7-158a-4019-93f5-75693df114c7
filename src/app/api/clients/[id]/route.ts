'use server';
import { NextResponse } from 'next/server';
// import supabase from '@/lib/supabase/client';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { dispatchKlaviyoEvent } from '@/lib/klaviyo/service';
import { KlaviyoActions } from '@/constants/klaviyo-actions';

// Define a GET handler for fetching the client by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const supabase = createSupabaseServer();
  const { id } = params;

  // First, get the client data
  const { data, error } = await supabase
    .from(tableNames.clients)
    .select(
      ` *,
      transactions(*),
      invoices(*, 
        slp:slp_id(*), 
        slp_notes:slp_notes_invoice_id_fkey(*),
        clients(display_name),
        invoice_items:invoice_items_invoice_id_fkey(*)
      ),
      bookings (*, slp:slp_id(*)),
      client_emails(*),
      packages(*, invoices(*), clients(*)),
      slp_notes:slp_notes_client_id_fkey(*),
      active_slp(*),
      refunds(*),
      tags(*),
      organization:organization_id(*),
      organization_id,
      form_answers:form_answers_client_id_fkey(*)
    `
    )
    .eq('id', id)
    .order('id', { foreignTable: 'invoices' })
    .order('appointment', { foreignTable: 'bookings', ascending: false });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  if (data.length === 0) {
    return NextResponse.json({ error: 'No client found' }, { status: 500 });
  }

  const clientData = data[0];

  // Check if current client is a linked client
  const { data: linkedClientCheck, error: linkedError } = await supabase
    .from('linked_clients')
    .select('client_id, added_client_id')
    .eq('added_client_id', id)
    .single();

  if (linkedError && linkedError.code !== 'PGRST116') {
    // PGRST116 is "not found" error
    console.error('Error checking linked client:', linkedError);
  }

  // If current client is a linked client, fetch parent's bookings and invoices
  if (linkedClientCheck) {
    const parentId = linkedClientCheck.client_id;

    // Get parent's bookings that match the current client's name
    const { data: parentBookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('*, slp:slp_id(*)')
      .eq('client_id', parentId)
      .eq('first_name', clientData.first_name)
      .eq('last_name', clientData.last_name)
      .order('appointment', { ascending: false });

    if (bookingsError) {
      console.error('Error fetching parent bookings:', bookingsError);
    } else if (parentBookings) {
      // Get existing booking IDs to avoid duplicates
      const existingBookingIds = new Set(
        clientData.bookings?.map((b: any) => b.id) || []
      );

      // Filter out any bookings that already exist
      const newParentBookings = parentBookings.filter(
        (booking: any) => !existingBookingIds.has(booking.id)
      );

      // Merge parent bookings with current client bookings
      clientData.bookings = [
        ...(clientData.bookings || []),
        ...newParentBookings,
      ];

      // Sort combined bookings by appointment date
      clientData.bookings.sort(
        (a: any, b: any) =>
          new Date(b.appointment).getTime() - new Date(a.appointment).getTime()
      );
    }

    // Optional: Add a flag to indicate this client has merged data
    clientData.has_parent_data = true;
    clientData.parent_client_id = parentId;
  }
  return NextResponse.json(clientData);

  // const { data, error } = await supabase
  //   .from(tableNames.clients)
  //   .select(
  //     ` *,
  //     transactions(*),
  //     invoices(*,
  //       slp:slp_id(*),
  //       slp_notes:slp_notes_invoice_id_fkey(*),
  //       clients(display_name),
  //       invoice_items:invoice_items_invoice_id_fkey(*)
  //     ),
  //     bookings (*, slp:slp_id(*)),
  //     client_emails(*),
  //     packages(*, invoices(*), clients(*)),
  //     slp_notes:slp_notes_client_id_fkey(*),
  //     active_slp(*),
  //     refunds(*),
  //     tags(*),
  //     organization:organization_id(*),
  //     organization_id,
  //     form_answers:form_answers_client_id_fkey(*)  // Add this line
  //   `
  //   )
  //   .eq('id', id)
  //   .order('id', { foreignTable: 'invoices' })
  //   .order('appointment', { foreignTable: 'bookings', ascending: false });

  // if (error) {
  //   return NextResponse.json({ error: error.message }, { status: 500 });
  // }
  // if (data.length === 0) {
  //   return NextResponse.json({ error: 'No client found' }, { status: 500 });
  // }

  //return NextResponse.json(data?.[0]);
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  const supabase = createSupabaseServer();
  const { id } = params;
  const { payload, initialData } = await request.json();
  console.log('payload', payload);
  if (!id) {
    return NextResponse.json({ message: 'ID is required' }, { status: 400 });
  }
  if (!payload) {
    return NextResponse.json(
      { message: 'Payload is required' },
      { status: 400 }
    );
  }

  const { email, ...rest } = payload;

  //Update client info
  const { data: updatedClient, error: clientError } = await supabase
    .from(tableNames.clients)
    .update(rest)
    .eq('id', id)
    .select();

  if (clientError) {
    return NextResponse.json({ error: clientError.message }, { status: 500 });
  }

  if (initialData && initialData.stage !== payload.stage) {
    const email = initialData?.client_emails?.find(
      (item: any) => item.is_primary_email === true
    )?.email;
    if (!email) {
      return;
    }
    await dispatchKlaviyoEvent(
      initialData.organization_id,
      supabase,
      {
        data: {
          type: 'event',
          attributes: {
            profile: {
              data: {
                type: 'profile',
                attributes: {
                  email,
                  first_name: payload?.first_name,
                  last_name: payload?.last_name,
                  id: initialData.id,
                },
              },
            },
            metric: {
              data: {
                type: 'metric',
                attributes: {
                  name: 'Stage Updated',
                },
              },
            },
            properties: {
              new_stage: payload.stage,
            },
          },
        },
      },
      KlaviyoActions.STAGE_UPDATED
    );
  }
  // return NextResponse.json(updatedClient?.[0]);
  //Update primary email if provided
  if (email) {
    const { error: emailError } = await supabase
      .from(tableNames.client_emails)
      .update({ email })
      .eq('client_id', id)
      .eq('is_primary_email', true);

    if (emailError) {
      return NextResponse.json({ error: emailError.message }, { status: 500 });
    }
  }

  if (!updatedClient?.length) {
    return NextResponse.json({ message: 'Client not found' }, { status: 404 });
  }

  return NextResponse.json(updatedClient[0]);
}
