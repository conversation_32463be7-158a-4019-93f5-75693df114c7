import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const getClient = async (
  supabase: SupabaseClient,
  form: {
    first_name: string;
    last_name: string;
    middle_name: string;
    email: string;
    phone_number: string;
    stage: string;
    province: string;
    organization_id: number | string;
  },
  org_id: any,
  user_id: any
) => {
  console.log('org_id', org_id);
  console.log('user_id', user_id);
  console.log('form', form);
  console.log('Trying to check if existing client exist ===');
  const { data: clientExist, error: ClientExistError } = await supabaseAdmin
    .from(tableNames.client_emails)
    .select(`id, client_id, email`)
    .eq('email', form.email)
    .eq('organization_id', Number(org_id))
    .maybeSingle();

  console.log('clientExist res is ', clientExist);

  if (ClientExistError) {
    throw ClientExistError;
  }
  if (clientExist) {
    return;
  }

  const client_obj: any = {
    //email: form.email.toLowerCase(),
    active_slp: Number(user_id),
    organization_id: Number(form?.organization_id) || Number(org_id),
    first_name: form.first_name || '',
    last_name: form.last_name || '',
    middle_name: form.middle_name || '',
    display_name: `${form.first_name} ${form.last_name}`,
    phone: form.phone_number || '',
    province: form.province || '',
    lead_created: new Date() || '',
    stage: form.stage,
  };

  console.log('Trying to create new client ', client_obj);

  const { data: NewClientData, error: NewClientError } = await supabaseAdmin
    .from(tableNames.clients)
    .insert(client_obj)
    .select()
    .single();

  console.log('NewClientError', NewClientError);

  if (NewClientError) {
    throw NewClientError;
  }

  const client_email_obj = {
    client_id: NewClientData?.id,
    email: form.email.toLowerCase(),
    is_primary_email: true,
    organization_id: Number(form.organization_id) || Number(org_id),
  };

  console.log('Trying to create client email ', client_email_obj);

  const { data: NewClientEmail, error: NewClientEmailError } =
    await supabaseAdmin
      .from(tableNames.client_emails)
      .insert(client_email_obj)
      .select();

  console.log('NewClientEmail is ', NewClientEmail);

  if (NewClientEmailError) {
    throw NewClientEmailError;
  }

  // update client activity when a new client is created.
  const client_activity_obj = {
    client_id: NewClientData.id,
    organization_id: Number(form.organization_id) || Number(org_id),
    activity_type: 'client_created',
    activity_date: new Date().toISOString(),
    details: { created_by: 'importing' },
  };
  console.log('trying to create activity ', client_activity_obj);

  const { error: ClientActivityError } = await supabaseAdmin
    .from(tableNames.client_activities)
    .insert(client_activity_obj);

  if (ClientActivityError) {
    throw ClientActivityError;
  }

  return ClientActivityError;
};
export async function POST(request: NextRequest) {
  const body = await request.json();
  const supabase = createSupabaseServer();

  if (!Array.isArray(body) || body.length === 0) {
    return NextResponse.json(
      { message: 'Invalid request object' },
      { status: 500 }
    );
  }

  for (const client of body) {
    if (!client.email || !client.first_name || !client.last_name) {
      return NextResponse.json(
        { message: 'First name, Last name and Email is required' },
        { status: 500 }
      );
    }
    await getClient(supabase, client, client.organization_id, client.user_id);
  }
  // if (error) {
  //   return NextResponse.json({ message: error.message }, { status: 500 });
  // }

  return NextResponse.json(
    { message: 'All clients imported' },
    { status: 200 }
  );
}
