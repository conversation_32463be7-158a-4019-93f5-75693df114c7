import { env } from '@/constants/env';
import { permissions } from '@/constants/permissions';
import { tableNames } from '@/constants/table_names';
import formTemplate from '@/lib/form-template/formTemplate';
import { createSupabaseServer } from '@/lib/supabase/server';
import { createClient } from '@supabase/supabase-js';
import moment from 'moment';
import { customAlphabet, nanoid } from 'nanoid'; // nanoid is a small library for generating unique IDs
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
const nanoidAlphaNum = customAlphabet(
  '0123456789abcdefghijklmnopqrstuvwxyz',
  6
);

const listOfPermissionsNeeded = [
  permissions.canViewProfile,
  permissions.canViewSchedules,
  permissions.canViewScheduleBookings,
  permissions.canViewEvents,
  permissions.canViewBilling,
  permissions.canViewBillingInvoices,
  permissions.canViewBillingPackages,
  permissions.canViewSlpSessions,
  permissions.canViewSlpClients,
  permissions.canViewSlpInvoices,
  permissions.canViewProducts,
  permissions.canViewClientSoapNotes,
];

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);
const generateSlug = () => {
  const randomPart = nanoidAlphaNum(); // alphanumeric 6 chars
  return `organization-${randomPart}`;
};
const isSlugUnique = async (slug: string) => {
  const { data, error } = await supabaseAdmin
    .from(tableNames.organizations)
    .select('id')
    .eq('slug', slug)
    .single();

  return !data && !error;
};

const createOrganization = async ({ logo_url, form, userFromServer }: any) => {
  console.log('trying to create an organization');

  const contactStagesForNewUser = [
    {
      id: 'lead',
      label: 'Lead',
      isDefault: true,
      color: '#3B82F6',
    },
    {
      id: 'prospect',
      label: 'Prospect',
      isDefault: true,
      color: '#EF4444',
    },
    {
      id: 'sql',
      label: 'SQL',
      isDefault: true,
      color: '#F59E0B',
    },
    {
      id: 'customer',
      label: 'Customer',
      isDefault: true,
      color: '#22C55E',
    },
    {
      id: 'closed-lost',
      label: 'Closed Lost',
      isDefault: true,
      color: '#6366F1',
    },
  ];

  let slug = '';
  let attemptCount = 0;
  const maxAttempts = 10;

  while (attemptCount < maxAttempts) {
    slug = generateSlug();
    const isUnique = await isSlugUnique(slug);
    if (isUnique) break;
    attemptCount++;
  }
  if (!slug) {
    return NextResponse.json(
      { error: 'Unable to generate a unique slug' },
      { status: 500 }
    );
  }

  const { data: createdOrganization, error: orgError } = await supabaseAdmin

    .from(tableNames.organizations)
    .insert({
      logo_url,
      contact_stages: contactStagesForNewUser,
      name: form.organization_name || `unnamed-organization-${nanoid(6)}`,
      owner: userFromServer?.id,
      slug,
      billing_info: {
        country: form?.country,
      },
    })
    .select('*')
    .single();

  if (orgError) {
    console.error('Error in orgError request:', orgError);
    return NextResponse.json({ error: orgError.message }, { status: 500 });
  }
  return createdOrganization;
};

const updateUser = async ({
  createdOrganization,
  form,
  timezone,
  userFromServer,
}: any) => {
  console.log('Trying to update a user');
  const currentDateTime = new Date().toISOString();

  const { data: PermissionsData, error: permissionsError } = await supabaseAdmin
    .from(tableNames.permissions)
    .select('id')
    .in('name', listOfPermissionsNeeded);

  if (permissionsError) {
    console.error('Error in permissionsError request:', permissionsError);
    return NextResponse.json(
      { error: permissionsError.message },
      { status: 500 }
    );
  }
  const determineTitle = () => {
    if (form?.occupation) {
      if (['Other', 'other'].includes(form?.occupation)) {
        return form?.other ?? userFromServer?.office_title;
      }
      return form?.occupation;
    } else {
      return userFromServer?.office_title;
    }
  };
  const permissions_id = PermissionsData?.map((row) => String(row.id));
  const payload = {
    organization_id: createdOrganization.id,
    permissions_id: permissions_id,
    ...(form?.first_name && form?.last_name
      ? { first_name: form.first_name, last_name: form.last_name }
      : {}),
    timezone,
    office_title: determineTitle(),
    last_login_dt: currentDateTime,
  };

  const { error: updatedError } = await supabaseAdmin
    .from(tableNames.users)
    .update(payload)
    .eq('id', userFromServer?.id)
    .select('*');

  if (updatedError) {
    console.error('Error in updatedError request:', updatedError);
    return NextResponse.json({ error: updatedError.message }, { status: 500 });
  }
};

const insertDefaultSubscription = async ({ createdOrganization }: any) => {
  const { error: subError } = await supabaseAdmin
    .from(tableNames.subscriptions)
    .insert({
      organization_id: createdOrganization.id,
      plan_id: 1,
      billing_cycle: 'MONTHLY',
      status: 'ACTIVE',
      start_date: new Date(),
      end_date: moment().add(1, 'month'),
    });

  if (subError) {
    console.error('Error in subError request:', subError);
    return NextResponse.json({ error: subError.message }, { status: 500 });
  }
};

const createDefaultClient = async ({
  userFromServer,
  createdOrganization,
}: any) => {
  console.log('Trying to create a default client');

  const { data, error } = await supabaseAdmin
    .from(tableNames.clients)
    .insert({
      active_slp: userFromServer?.id,
      slp_notes: 'Active',
      first_name: 'John',
      last_name: 'Smith(Demo User)',
      stage: 'Customer',
      lead_created: new Date().toISOString(),
      email: '<EMAIL>',
      display_name: 'John Smith(Demo User)',
      organization_id: createdOrganization.id,
    })
    .select('*')
    .single();
  if (error) {
    throw new Error(error.message);
  }
  return data;
};

// const createDefaultProduct = async ({ createdOrganization }: any) => {
//   console.log('Trying to create a default product');

//   const { data: productData, error: productError } = await supabaseAdmin
//     .from(tableNames.products)
//     .insert([
//       {
//         organization_id: createdOrganization.id,
//         name: 'Quick Connect - 30-Minute Session',
//         description:
//           'A focused 30-minute session for quick discussions, consultations, or check-ins.',
//         price: 30,
//         minutes: 30,
//         status: 'ACTIVE',
//       },
//     ])
//     .select('*')
//     .single();

//   if (productError) {
//     console.error('Error in productError request:', productError);
//     return NextResponse.json({ error: productError.message }, { status: 500 });
//   }
//   return productData;
// };
const createDefaultService = async ({
  createdOrganization,
  userFromServer,
}: any) => {
  console.log('Trying to create a default service');

  const { data: serviceData, error: serviceError } = await supabaseAdmin
    .from(tableNames.services)
    .insert([
      {
        organization_id: createdOrganization.id,
        created_by: userFromServer?.id,
        name: 'Initial Consultation',
        description:
          'A 60-minute session to assess client needs and establish goals.',
        price: 120.0,
        duration_minutes: 60,
        status: 'ACTIVE',
      },
    ])
    .select('*')
    .single();

  if (serviceError) {
    console.error('Error in productError request:', serviceError);
    return NextResponse.json({ error: serviceError.message }, { status: 500 });
  }
  return serviceData;
};

const createDefaultForm = async ({
  createdOrganization,
  userFromServer,
}: any) => {
  console.log('Trying to create a default form');
  console.log('createdOrganization--55', createdOrganization);

  try {
    const formPayload = formTemplate(createdOrganization, userFromServer);
    console.log('formPayload', formPayload);

    const { data: formData, error: formError } = await supabaseAdmin
      .from(tableNames.forms)
      .insert(formPayload)
      .select('*');

    if (formError) {
      console.error('Error inserting form:', formError);
      throw formError; // This will be caught by the outer try/catch
    }

    console.log('Form created successfully:', formData);
    return formData; // Return the created form data
  } catch (error) {
    console.error('Error in createDefaultForm:', error);
    throw error; // Re-throw to be handled by the main POST handler
  }
};

const createDefaultBooking = async ({
  createdService,
  createdClient,
  userFromServer,
  createdOrganization,
}: any) => {
  console.log('Trying to create a default booking');

  const now = new Date();
  const { data } = await supabaseAdmin
    .from(tableNames.bookings)
    .insert({
      organization_id: createdOrganization.id,
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone: null,
      province: null,
      client_id: createdClient.id,
      event: createdService.name,
      service_id: createdService?.id,
      assigned_to: userFromServer.email,
      slp_id: userFromServer.id,
      appointment: moment(now).utc().format('YYYY-MM-DD HH:mm:ss+00'),
      booking_created_at_raw: moment(now)
        .utc()
        .format('YYYY-MM-DD HH:mm:ss+00'),
      appointment_raw: moment.utc(now).format('MM/DD/YYYY HH:mm:ss'),
    })
    .select('*');
  return data;
};

const updateAuthSession = async ({ supabase }: any) => {
  console.log('Trying to update session');

  const cookieStore = cookies();

  const { data: userSession, error: errorSession } =
    await supabase.auth.refreshSession();

  if (errorSession) {
    console.error('Error in errorSession request:', errorSession);
    return NextResponse.json({ error: errorSession.message }, { status: 500 });
  }

  const { data: fullUser } = await supabase.rpc('get_user_by_email', {
    user_email: userSession.session?.user?.email,
  });

  const expirationDate = new Date(
    (userSession.session?.expires_at as number) * 1000
  );
  expirationDate.setFullYear(expirationDate.getFullYear() + 1);

  cookieStore.set({
    name: 'user_data',
    value: JSON.stringify(fullUser),
    expires: expirationDate,
  });
  return fullUser;
};

export async function POST(request: NextRequest) {
  try {
    const { logo_url, form, userFromServer, timezone } = await request.json();
    const supabase = createSupabaseServer();

    console.log('userFromServer is ', userFromServer);

    // CREATE ORGANIZATION
    const createdOrganization = await createOrganization({
      logo_url,
      form,
      userFromServer,
    });
    console.log('createdOrganization', createdOrganization);
    // UPDATE USER DATA
    await updateUser({ createdOrganization, form, timezone, userFromServer });

    // CREATE DEFAULT SUBSCRIPTION
    await insertDefaultSubscription({ createdOrganization });

    // CREATE DEFAULT CLIENT
    const createdClient = await createDefaultClient({
      userFromServer,
      createdOrganization,
    });
    console.log('created createdClient >>>>>', createdClient);

    // CREATE DEFAULT SERVICE
    const createdService = await createDefaultService({
      createdOrganization,
      userFromServer,
    });
    // const createdProduct = await createDefaultProduct({ createdOrganization });

    // CREATE DEFAULT BOOKING
    await createDefaultBooking({
      createdClient,
      createdService,
      userFromServer,
      createdOrganization,
    });

    // CREATE DEFAULT FORM
    await createDefaultForm({ createdOrganization });

    // UPDATE SESSION
    const fullUser = await updateAuthSession({ supabase });

    return NextResponse.json({ data: fullUser }, { status: 200 });
  } catch (error: any) {
    console.error('Error in POST request:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
