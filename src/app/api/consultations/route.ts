import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import moment from 'moment/moment';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const selectedDate = searchParams.get('selectedDate');
  const organization_id = searchParams.get('organization_id');
  console.log('organization_id', organization_id);
  const supabase = createSupabaseServer();

  let query = supabase
    .from(tableNames.bookings)
    .select(`*, clients (*) , notes (*)`)
    .limit(50)
    .gte('appointment', moment.utc(selectedDate).startOf('day').toISOString())
    .lt('appointment', moment.utc(selectedDate).endOf('day').toISOString())
    .like('event', '%onsultation%')
    .order('appointment');

  // .or('calendly_event_type.neq.invitee.canceled,calendly_event_type.is.null')

  // Add organization_id filter if provided
  if (organization_id) {
    query = query.eq('organization_id', Number(organization_id));
  }

  const { data, error } = await query;

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
}
