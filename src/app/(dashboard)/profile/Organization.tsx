import StringInput from '@/components/Input/StringInput';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { Button } from '@/components/ui/button';
import {
  Avatar,
  Box,
  Center,
  Flex,
  GridItem,
  Heading,
  Icon,
  Progress,
  SimpleGrid,
  Stack,
  Text,
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuSeparator,
  MenuTrigger,
} from '@chakra-ui/react';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
// import { BsThreeDotsVertical } from 'react-icons/bs';
// import CustomTextArea from '@/components/Input/CustomTextArea';
import { FileUploadRoot, FileUploadTrigger } from '@/components/ui/file-upload';
import { toaster } from '@/components/ui/toaster';
import CustomTable from '@/components/table/CustomTable';
import { createColumnHelper } from '@tanstack/react-table';

import CustomSelect from '@/components/Input/CustomSelect';
import { Field } from '@/components/ui/field';
import {
  NumberInputField,
  NumberInputRoot,
} from '@/components/ui/number-input';
import { currencyOptions } from '@/data/options';
import { TUseSlpHook } from '@/hooks/slp/useSlpHook';
import { AiOutlineExclamationCircle } from 'react-icons/ai';
import { FiEdit, FiPlus, FiTrash2 } from 'react-icons/fi';
import { HiUpload } from 'react-icons/hi';
import { IoMdCheckmarkCircle } from 'react-icons/io';
import useTaxHook from '../create-invoice/_hook/useTaxHook';
import useContactStagesHook from './useContactStagesHook';

import { BsThreeDotsVertical } from 'react-icons/bs';

// Create column helper for tax table
const columnHelper = createColumnHelper<any>();

// Tax table column definitions
const createTaxColumnDef = (
  setFormData: any,
  onDeleteOpen: any,
  handleInputChange: any
) => [
  columnHelper.accessor('name', {
    cell: (info) => (
      <Box fontWeight={'medium'}>
        {info.getValue()} - ({info.row.original.value}%)
      </Box>
    ),
    header: 'Tax Name & Rate',
    id: 'name',
  }),
  columnHelper.accessor('description', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Description',
    id: 'description',
  }),
  columnHelper.display({
    cell: (props) => (
      <Box position={'relative'}>
        <MenuRoot>
          <MenuTrigger cursor={'pointer'}>
            <BsThreeDotsVertical />
          </MenuTrigger>
          <MenuContent
            cursor={'pointer'}
            position={'absolute'}
            zIndex={10}
            right={0}
            top={'-4.5'}
          >
            <MenuItem value="edit">
              <FiEdit
                cursor={'pointer'}
                onClick={() => {
                  setFormData({
                    ...props.row.original,
                    value: props.row.original.value.toString(),
                  });
                }}
              />
              <Text>Edit</Text>
            </MenuItem>
            <MenuSeparator />
            <MenuItem value="delete">
              <FiTrash2
                color="red"
                onClick={() => {
                  onDeleteOpen();
                  handleInputChange('id', props.row.original.id);
                }}
                cursor={'pointer'}
              />
              <Text>Delete</Text>
            </MenuItem>
          </MenuContent>
        </MenuRoot>
      </Box>
    ),
    header: 'Actions',
    id: 'actions',
  }),
];

// Contact Stages table column definitions
const createContactStagesColumnDef = (
  setFormData: any,
  onDeleteOpen: any,
  handleInputChange: any
) => [
  columnHelper.accessor('label', {
    cell: (info) => <Box fontWeight={'medium'}>{info.getValue()}</Box>,
    header: 'Stage Name',
    id: 'label',
  }),
  columnHelper.accessor('isDefault', {
    cell: (info) => (
      <Box>
        {info.getValue() ? (
          <Text color="green.600" fontWeight="medium">
            Yes
          </Text>
        ) : (
          <Text color="gray.500">No</Text>
        )}
      </Box>
    ),
    header: 'Default',
    id: 'isDefault',
  }),
  columnHelper.display({
    cell: (props) => (
      <Box position={'relative'}>
        <MenuRoot>
          <MenuTrigger cursor={'pointer'}>
            <BsThreeDotsVertical />
          </MenuTrigger>
          <MenuContent
            cursor={'pointer'}
            position={'absolute'}
            zIndex={10}
            right={0}
            top={'-4.5'}
          >
            <MenuItem value="edit">
              <FiEdit
                cursor={'pointer'}
                onClick={() => {
                  setFormData(props.row.original);
                }}
              />
              <Text>Edit</Text>
            </MenuItem>
            {!props.row.original.isDefault && (
              <>
                <MenuSeparator />
                <MenuItem value="delete">
                  <FiTrash2
                    color="red"
                    onClick={() => {
                      onDeleteOpen();
                      handleInputChange('id', props.row.original.id);
                    }}
                    cursor={'pointer'}
                  />
                  <Text>Delete</Text>
                </MenuItem>
              </>
            )}
          </MenuContent>
        </MenuRoot>
      </Box>
    ),
    header: 'Actions',
    id: 'actions',
  }),
];

export function Organization({ hook }: { hook: TUseSlpHook }) {
  const {
    data: SlpData,
    payload,
    isLoading,
    handleChange,
    handleSubmit,
    setPayload,
    isEdit,
    isFetching,
    setOrganizationForm,
    organizationForm,
    OrganizationBySlugData,
    OrganizationBySlugLoading,
    OrganizationBySlugSuccess,
    countryOptions,
    stateOptions,
    cityOptions,
  } = hook;
  const {
    formData,
    handleInputChange,
    handleSubmit: saveTax,
    // errors,
    // taxLoading,
    isLoading: saveTaxLoading,
    taxData,
    setFormData,
    handleDelete,
    deleteLoading,
    // updateTax,
    updateLoading,
    isDeleteOpen,
    onDeleteClose,
    onDeleteOpen,
  } = useTaxHook({});

  // Contact Stages Hook
  const {
    formData: contactStageFormData,
    handleInputChange: handleContactStageInputChange,
    handleSubmit: saveContactStage,
    errors: contactStageErrors,
    isLoading: saveContactStageLoading,
    handleDelete: handleContactStageDelete,
    setFormData: setContactStageFormData,
    isDeleteOpen: isContactStageDeleteOpen,
    onDeleteClose: onContactStageDeleteClose,
    onDeleteOpen: onContactStageDeleteOpen,
  } = useContactStagesHook({
    contactStages: payload?.contact_stages || [],
    onUpdateContactStages: (stages: any[]) => {
      setPayload((prev: any) => ({ ...prev, contact_stages: stages }));
    },
  });

  console.log('payload', payload);

  // const taxRateOptions = Array.from({ length: 101 }).map((_, index) => {
  //   return { value: index, label: `${index}%` };
  // });

  // console.log('SlpData is ', SlpData);
  // console.log('cities', cities);
  console.log('taxData', taxData);

  if (isFetching)
    return (
      <Center h={'20rem'}>
        <AnimateLoader />
      </Center>
    );

  return (
    <Stack
      border={'1px solid #d1d5db'}
      rounded={'10px'}
      px={'4'}
      py={'6'}
      gap={'6'}
    >
      <Stack gap={'-2.5'}>
        <Heading>Company Information</Heading>
        <Text fontSize={'sm'} color={'#9ca3af'}>
          Manage your business details and company profile
        </Text>
      </Stack>
      <SimpleGrid gap={'1.5rem'} row={2} columns={{ base: 1, lg: 1 }}>
        <GridItem>
          <StringInput
            inputProps={{
              name: 'name',
              type: 'text',
              placeholder: 'Organization name',
              defaultValue: payload?.name || SlpData?.organization?.name,
              onChange: (e) => handleChange(e, 'name'),
            }}
            fieldProps={{
              required: true,
              label: 'Name',
              disabled: !isEdit,
            }}
          />
        </GridItem>
        <GridItem spaceY={'1.5rem'}>
          {/* <CustomTextArea
            inputProps={{
              name: 'billing_info',
              placeholder: 'Billing Info',
              defaultValue:
                payload?.billing_info || SlpData?.organization?.billing_info,
              onChange: (e) => handleChange(e, 'billing_info'),
              minH: '52',
            }}
            fieldProps={{
              label: 'Billing Info',
              disabled: !isEdit,
            }}
          /> */}
          <StringInput
            inputProps={{
              type: 'text',
              name: 'address',
              defaultValue: payload?.address,
              // placeholder: 'Address',
              value: payload.address,
              onChange: (e) => handleChange(e, 'address'),
            }}
            fieldProps={{ label: 'Address' }}
          />

          <Box>
            <CustomSelect
              placeholder="Select Currency"
              onChange={(option) => handleChange(option.value, 'currency_code')}
              options={currencyOptions}
              label="Currency Code"
              selectedOption={currencyOptions?.find(
                (option) => option.value === payload?.currency_code
              )}
            />
          </Box>

          {/* country */}
          <CustomSelect
            onChange={(option) => {
              handleChange(option.value, 'country');
            }}
            selectedOption={countryOptions?.find(
              (option) => option?.value?.isoCode == payload?.country?.isoCode
            )}
            options={countryOptions}
            label="Country"
          />
          {/* state */}
          <CustomSelect
            onChange={(option) => {
              handleChange(option.value, 'state');
            }}
            selectedOption={stateOptions?.find(
              (option) => option?.value?.isoCode == payload?.state?.isoCode
            )}
            options={stateOptions}
            label="State/Province"
          />
          {/* city */}
          <CustomSelect
            onChange={(option) => {
              handleChange(option.value, 'city');
            }}
            selectedOption={cityOptions?.find(
              (option) => option?.value?.name == payload?.city?.name
            )}
            options={cityOptions}
            label="City"
          />
          {/* <StringInput
            inputProps={{
              defaultValue: SlpData?.organization?.billing_info?.country,
              // placeholder: 'Country',
              value: payload.country,
              onChange: (e) => handleChange(e, 'country'),
            }}
            fieldProps={{ label: 'Country' }}
          /> */}

          <StringInput
            inputProps={{
              defaultValue: SlpData?.organization?.billing_info?.phone,
              // placeholder: 'Telephone',
              value: payload.phone,
              onChange: (e) => handleChange(e, 'phone'),
            }}
            fieldProps={{ label: 'Telephone' }}
          />
          <StringInput
            inputProps={{
              defaultValue: SlpData?.organization?.billing_info?.website,
              // placeholder: 'Website',
              value: payload.website,
              onChange: (e) => handleChange(e, 'website'),
            }}
            fieldProps={{ label: 'Website' }}
          />
        </GridItem>

        {/* {SlpData && SlpData?.organization?.plan === 'TEAM' && ( */}
        <GridItem>
          <Text pb={'2'} fontWeight={'medium'}>
            Company Logo
          </Text>

          <Avatar.Root w="12rem" h="6rem" rounded={'2px'} bg={'gray.50'}>
            <Avatar.Fallback>
              {`${SlpData?.organization?.name?.substring(0, 1)}`}
            </Avatar.Fallback>
            <Avatar.Image
              rounded={0}
              src={
                payload?.logoFile
                  ? URL.createObjectURL(payload?.logoFile)
                  : SlpData?.organization?.logo_url || ''
              }
              alt="Organization Logo"
              objectFit={'contain'}
            />
          </Avatar.Root>
          <FileUploadRoot
            pt={'4'}
            maxFiles={1}
            maxFileSize={1048576} // 1MB
            onFileChange={async (files: any) => {
              const selectedFile = files?.acceptedFiles?.[0];

              if (selectedFile) {
                const img = new Image();
                img.src = URL.createObjectURL(selectedFile);

                img.onload = () => {
                  const maxWidth = 400;
                  const maxHeight = 120;

                  if (img.width > maxWidth || img.height > maxHeight) {
                    toaster.create({
                      description: `Logo dimensions should not exceed 400x120 pixels.`,
                      type: 'error',
                    });
                  } else {
                    setPayload((prev: any) => ({
                      ...prev,
                      logoFile: selectedFile,
                    }));
                  }
                };

                img.onerror = () => {
                  toaster.create({
                    description: 'Invalid image file.',
                    type: 'error',
                  });
                };
              }
            }}
            onFileReject={(file) => {
              if (file.files[0]?.errors[0]) {
                toaster.create({
                  description: file.files[0]?.errors[0],
                  type: 'error',
                });
              }
            }}
            disabled={!isEdit}
            accept={['image/jpeg', 'image/png']}
          >
            <FileUploadTrigger asChild>
              <Button variant="outline" size="md" w={'full'}>
                <HiUpload /> Upload new Logo
              </Button>
            </FileUploadTrigger>

            {/* <FileUploadList clearable /> */}
          </FileUploadRoot>
          <Text fontSize={'xs'}>
            Logo dimension should not be more than 400x120
          </Text>
        </GridItem>
        {/* )} */}
      </SimpleGrid>
      <Box mt={'1rem'}>
        <Heading fontWeight={600}>Organization Slug</Heading>
        <Text fontSize={'.8rem'}>
          Changing your organization slug will mean that all of your copied
          links will no longer work and will need to be updated.
        </Text>
        {/* ========================== */}
        <Flex alignItems={'center'} gap={'1rem'}>
          {/* <Text>{env.FRONTEND_URL}/book</Text> */}
          <StringInput
            inputProps={{
              defaultValue: SlpData?.organization?.slug,
              fontSize: '1rem',
              value: organizationForm.tempSearch,
              onChange: (e) =>
                setOrganizationForm((prev: any) => ({
                  ...prev,
                  tempSearch: e.target.value?.trim()?.toLowerCase(),
                })),
            }}
            fieldProps={{ required: true }}
          />
        </Flex>
        <Stack gap={'2'} ml={'auto'} w={'10rem'} mt={'1rem'}>
          {OrganizationBySlugLoading && organizationForm?.search && (
            <Box>
              <Progress.Root maxW="100%" value={null} colorPalette={'orange'}>
                <Progress.Track>
                  <Progress.Range />
                </Progress.Track>
              </Progress.Root>
            </Box>
          )}
          {OrganizationBySlugSuccess &&
            OrganizationBySlugData?.length === 0 && (
              <Flex
                w={'100%'}
                color={'green.600'}
                alignItems={'center'}
                gap={'.8rem'}
                justifyContent={'flex-end'}
              >
                <Icon boxSize={'1.2rem'}>
                  <IoMdCheckmarkCircle />
                </Icon>
                <Text>available</Text>
              </Flex>
            )}
          {OrganizationBySlugSuccess && OrganizationBySlugData?.length > 0 && (
            <Flex
              w={'100%'}
              color={'red.600'}
              alignItems={'center'}
              gap={'.8rem'}
              justifyContent={'flex-end'}
            >
              <Icon boxSize={'1.2rem'}>
                <AiOutlineExclamationCircle />
              </Icon>
              <Text>unavailable</Text>
            </Flex>
          )}
        </Stack>
        {/* ========================== */}
      </Box>

      <Flex
        gap={'1rem'}
        alignItems={'center'}
        justifyContent={'flex-end'}
        // mb={'2rem'}
        // mt={'1rem'}
        // pr={'1.6rem'}
      >
        <Button
          disabled={
            !isEdit || (OrganizationBySlugLoading && organizationForm.search)
          }
          loading={isLoading}
          minH={'2.5rem'}
          fontSize={'.9rem'}
          minW={'8rem'}
          bg={'primary.500'}
          onClick={handleSubmit}
        >
          Save Changes
        </Button>
      </Flex>

      {/* <MyLink user={user} /> */}
      <GridItem>
        <Text fontWeight={'bold'}>Tax Setting</Text>
        <Text>Manage your tax rates and settings for your business</Text>
        <Box mt={'2rem'} p={'1rem'} border={'1px solid #d1d5db'}>
          <Box
            fontWeight={'bold'}
            display={'flex'}
            gap={'0.5rem'}
            alignItems={'center'}
          >
            <FiPlus />
            <Text>Add New Tax</Text>
          </Box>
          <Box
            display={'flex'}
            gap={'10px'}
            w={'100%'}
            mt={'1rem'}
            alignItems={'flex-end'}
            justifyContent={'space-between'}
          >
            <Box w={'20%'}>
              <StringInput
                inputProps={{
                  value: formData.name,
                  onChange: (e) => handleInputChange('name', e.target.value),
                }}
                fieldProps={{ label: 'Tax Name' }}
              />
            </Box>
            <Box w={'30%'}>
              <StringInput
                inputProps={{
                  value: formData.description,
                  onChange: (e) =>
                    handleInputChange('description', e.target.value),
                }}
                fieldProps={{ label: 'Description' }}
              />
            </Box>
            <Box w={'20%'}>
              <Field label="Rate %" flex={1}>
                <NumberInputRoot
                  showStepper={false}
                  value={formData?.value}
                  onValueChange={(e) => {
                    handleInputChange('value', e?.value);
                  }}
                >
                  <NumberInputField />
                </NumberInputRoot>
              </Field>
            </Box>
            <Box w={'auto'}>
              <Button
                onClick={saveTax}
                loading={saveTaxLoading || updateLoading}
                disabled={saveTaxLoading || updateLoading}
              >
                {formData?.id ? 'Update' : 'Save'}
              </Button>
            </Box>
          </Box>
        </Box>
      </GridItem>

      {taxData?.data?.length ? (
        <Box>
          {/* <Heading fontWeight={600} mb={2}>
            Taxes
          </Heading> */}
          <CustomTable
            columnDef={createTaxColumnDef(
              setFormData,
              onDeleteOpen,
              handleInputChange
            )}
            data={taxData?.data || []}
            minHeight="200px"
            enableSorting={false}
            NoDataText="No taxes configured"
          />
        </Box>
      ) : null}

      <ConsentDialog
        handleSubmit={() => handleDelete(formData?.id)}
        open={isDeleteOpen}
        onOpenChange={() => {
          onDeleteClose();
          handleInputChange('id', '');
        }}
        heading={'Confirm deletion?'}
        note="This will permanently delete this tax."
        isLoading={deleteLoading}
      />

      {/* Contact Stages Management */}
      <GridItem>
        <Text fontWeight={'bold'}>Contact Stages</Text>
        <Text>
          Manage your contact stages for lead tracking and organization
        </Text>
        <Box mt={'2rem'} p={'1rem'} border={'1px solid #d1d5db'}>
          <Box
            fontWeight={'bold'}
            display={'flex'}
            gap={'0.5rem'}
            alignItems={'center'}
          >
            <FiPlus />
            <Text>Add New Contact Stage</Text>
          </Box>
          <Box
            display={'flex'}
            gap={'10px'}
            w={'100%'}
            mt={'1rem'}
            alignItems={'flex-end'}
            justifyContent={'space-between'}
          >
            <Box w={'50%'}>
              <StringInput
                inputProps={{
                  value: contactStageFormData.label,
                  onChange: (e) =>
                    handleContactStageInputChange('label', e.target.value),
                }}
                fieldProps={{
                  label: 'Stage Name',
                  errorText: contactStageErrors.label,
                  invalid: !!contactStageErrors.label,
                }}
              />
            </Box>
            <Box w={'auto'}>
              <Button
                onClick={saveContactStage}
                loading={saveContactStageLoading}
                disabled={saveContactStageLoading}
              >
                {contactStageFormData?.id ? 'Update' : 'Save'}
              </Button>
            </Box>
          </Box>
        </Box>
      </GridItem>

      {payload?.contact_stages?.length ? (
        <Box>
          <CustomTable
            columnDef={createContactStagesColumnDef(
              setContactStageFormData,
              onContactStageDeleteOpen,
              handleContactStageInputChange
            )}
            data={payload?.contact_stages || []}
            minHeight="200px"
            enableSorting={false}
            NoDataText="No contact stages configured"
          />
        </Box>
      ) : null}

      <ConsentDialog
        handleSubmit={() => handleContactStageDelete(contactStageFormData?.id)}
        open={isContactStageDeleteOpen}
        onOpenChange={() => {
          onContactStageDeleteClose();
          handleContactStageInputChange('id', '');
        }}
        heading={'Confirm deletion?'}
        note="This will permanently delete this contact stage."
        isLoading={saveContactStageLoading}
      />
    </Stack>
  );
}
