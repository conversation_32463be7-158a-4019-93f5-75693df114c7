import { useUpdateInvoiceMutation } from '@/api/invoices/update-invoice';
import { useDeleteTransactionMutation } from '@/api/transactions/delete-transaction';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { determineInvoiceStatus } from '@/reuseables/invoice/helpers';
import {
  Box,
  // Center,
  Flex,
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuSeparator,
  MenuTrigger,
  // Icon,
  Separator,
  Text,
  chakra,
  useDisclosure,
} from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import moment from 'moment';
import { Fragment, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { IoWalletOutline } from 'react-icons/io5';
import EditPaymentForm from '../../slp/[id]/sessions/soapNoteFlow/EditPaymentForm';
import PaymentForm from '../../slp/[id]/sessions/soapNoteFlow/PaymentForm';
import { TLinkTransactionHook } from './_hook/useLinkInvoiceItemHook';

export default function RecordPayment({
  invoice,
  linkTransactionHook,
  transactions,
  amountDue,
}: {
  abbr?: any;
  invoice: any;
  linkTransactionHook: TLinkTransactionHook;
  transactions: any;
  amountDue: number;
}) {
  console.log('transactions----4', transactions);
  const paymentDisclosure = useDisclosure();
  const EditPaymentDisclosure = useDisclosure();
  const queryClient = useQueryClient();

  const {
    mutateAsync: DeleteTransaction,
    isLoading: DeleteTransactionLoading,
  } = useDeleteTransactionMutation();
  const deleteTransactionDisclosure = useDisclosure();
  const [selectedTransaction, setSelectedTransaction] = useState(null) as any;
  const { mutateAsync: updateInvoices } = useUpdateInvoiceMutation();

  // delete transaction
  const handleRemoveTransaction = async () => {
    if (!selectedTransaction?.id) {
      return;
    }
    await DeleteTransaction(selectedTransaction?.id);
    const statusData = {
      transactions: [
        ...invoice.transactions.filter(
          (transaction: any) => transaction.id !== selectedTransaction.id
        ),
      ],
      total_price: invoice.total_price,
      tax_value: 0,
      discount: invoice.discount || 0,
    };
    const invoiceStatus = determineInvoiceStatus(statusData);
    await updateInvoices({
      data: { status: invoiceStatus },
      id: invoice?.id,
    });
    deleteTransactionDisclosure.onClose();
    setSelectedTransaction(null);
    await queryClient.invalidateQueries({
      queryKey: ['newsf-get-invoice-by-id', Number(invoice?.id)],
    });
    await queryClient.invalidateQueries({
      queryKey: ['newsf-get-all-invoices'],
    });
  };

  return (
    <Box
      border={'1px solid #FEFEFE'}
      boxShadow={'lg'}
      rounded={'12px'}
      py={'1.5rem'}
      px={'10px'}
      w={'full'}
      minH={'6rem'}
      position={'relative'}
      maxH={'fit-content'}
    >
      <Box display={'flex'} width={'full'} gap={'1.25rem'} overflow={'hidden'}>
        <Box
          rounded={'full'}
          fontSize={'18px'}
          display={'flex'}
          justifyContent={'center'}
          alignItems={'center'}
          minW={'36px'}
          w={'36px'}
          h={'36px'}
          maxW={'36px'}
          cursor={'pointer'}
          color={'#E97A5B'}
          border={'2px solid #E97A5B'}
        >
          <IoWalletOutline />
        </Box>

        <Box overflow={'hidden'} width={'full'} maxWidth={'full'}>
          <Text color={'GrayText'}>Manage Payments</Text>
          {invoice?.id ? (
            <Box>
              <Flex gap={'.5rem'} mt={'1rem'} alignItems={'center'}>
                <Text>
                  <b>Amount Due:</b>{' '}
                  {formatMoney(amountDue, {
                    currencyCode: invoice?.currency_code,
                  })}
                </Text>
                {/* {!initialTransaction && ( */}
                {amountDue > 0 && (
                  <Flex gap={'.5rem'} alignItems={'center'}>
                    <Box w={'1rem'} h={'1px'} bg={'black'}></Box>

                    <Text>
                      <chakra.span
                        cursor={'pointer'}
                        color={'primary.500'}
                        fontWeight={500}
                        onClick={paymentDisclosure.onOpen}
                      >
                        Record a payment
                      </chakra.span>{' '}
                      manually
                    </Text>
                  </Flex>
                )}
                {amountDue === 0 && transactions && transactions?.length && (
                  <Text ml={'auto'}>
                    {' '}
                    <b>Status:</b> Your invoice is paid in full
                  </Text>
                )}
              </Flex>
              {transactions && transactions?.length
                ? transactions?.map((transaction: any) => (
                    <Fragment key={transaction.id}>
                      <Box>
                        <Separator my={'1rem'} />
                        <Flex>
                          <Box w={'full'}>
                            <Flex>
                              <Text>
                                {/* <b> Date</b>:{' '} */}
                                {/* {moment(transaction?.transaction_date).format(
                                  'MMMM D, YYYY'
                                )} */}
                                {moment(
                                  transaction?.transaction_date.split('T')[0]
                                ).format('MMM D, YYYY')}
                                :{' '}
                                <b>
                                  {/* {transaction?.currency_code === 'USD' && '$'} */}

                                  {formatMoney(transaction?.amount, {
                                    currencyCode: invoice?.currency_code,
                                  })}
                                  {/* ${transaction?.amount.toFixed(2)} */}
                                </b>{' '}
                                {transaction.transaction_type
                                  ?.toLowerCase()
                                  .replace(/_/g, ' ')}{' '}
                                received via{' '}
                                <b>
                                  {transaction?.payment_method
                                    ?.toLowerCase()
                                    .replace(/_/g, ' ')}
                                </b>
                              </Text>
                            </Flex>
                          </Box>
                          <Box w={'fit-content'} ml={'auto'}>
                            <MenuRoot>
                              <MenuTrigger cursor={'pointer'}>
                                <BsThreeDotsVertical />
                              </MenuTrigger>
                              <MenuContent
                                cursor={'pointer'}
                                position={'absolute'}
                                zIndex={10}
                                right={0}
                              >
                                {/* <MenuItem value="send">
                                  <Button
                                    p={'0'}
                                    h={'1rem'}
                                    fontWeight={600}
                                    color={'primary.500'}
                                    bg={'transparent !important'}
                                    outline={'none !important'}
                                  >
                                    Send a receipt
                                  </Button>
                                </MenuItem> */}
                                {/* <MenuSeparator /> */}
                                <MenuItem value="edit">
                                  <Button
                                    p={'0'}
                                    h={'1rem'}
                                    fontWeight={600}
                                    color={'primary.500'}
                                    bg={'transparent !important'}
                                    outline={'none !important'}
                                    justifyContent={'start'}
                                    w={'full'}
                                    onClick={() => {
                                      setSelectedTransaction(transaction);
                                      EditPaymentDisclosure.onOpen();
                                    }}
                                  >
                                    Edit payment
                                  </Button>
                                </MenuItem>
                                <MenuSeparator />
                                <MenuItem value="delete">
                                  <Button
                                    p={'0'}
                                    h={'1rem'}
                                    fontWeight={600}
                                    color={'primary.500'}
                                    bg={'transparent !important'}
                                    outline={'none !important'}
                                    justifyContent={'start'}
                                    w={'full'}
                                    onClick={() => {
                                      setSelectedTransaction(transaction);
                                      deleteTransactionDisclosure.onOpen();
                                    }}
                                  >
                                    Remove Payment
                                  </Button>
                                </MenuItem>
                              </MenuContent>
                            </MenuRoot>
                          </Box>
                        </Flex>
                      </Box>
                    </Fragment>
                  ))
                : null}
            </Box>
          ) : (
            <Text mt={'0.5rem'}>Please create an invoice</Text>
          )}
        </Box>
      </Box>

      {/* payment form */}
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        open={paymentDisclosure.open}
        onOpenChange={paymentDisclosure.onClose}
      >
        <PaymentForm
          linkTransactionHook={linkTransactionHook}
          invalidateQueryKey={'newsf-get-invoice-by-id'}
          initialBooking={{
            client_id: invoice?.client_id,
            id: invoice?.id,
            slp_notes: {
              invoice,
            },
          }}
          createPaymentDisclosure={paymentDisclosure}
          amountDue={amountDue}
        />
      </CustomModal>

      {/* Edit payment form */}
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        open={EditPaymentDisclosure.open}
        onOpenChange={EditPaymentDisclosure.onClose}
      >
        <EditPaymentForm
          invalidateQueryKey={'newsf-get-invoice-by-id'}
          initialBooking={{
            client_id: invoice?.client_id,
            id: invoice?.id,
            slp_notes: {
              invoice,
            },
          }}
          selectedTransaction={selectedTransaction}
          editPaymentDisclosure={EditPaymentDisclosure}
          amountDue={amountDue}
        />
      </CustomModal>

      <ConsentDialog
        open={deleteTransactionDisclosure.open}
        onOpenChange={deleteTransactionDisclosure.onClose}
        handleSubmit={handleRemoveTransaction}
        note={`This action will remove this transaction from this invoice.`}
        isLoading={DeleteTransactionLoading}
        heading={'Are you sure?'}
        // firstBtnText="Back"
        // secondBtnText="Yes, Disconnect"
      />
    </Box>
  );
}
