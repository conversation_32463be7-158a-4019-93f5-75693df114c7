/* eslint-disable react-hooks/exhaustive-deps */

'use client';
import { useGetClientByIdQuery } from '@/api/clients/get-client-by-id';
import {
  useCreateInvoiceMutation,
  useGetAllTaxesQuery,
} from '@/api/newsf/queries';
import { useGetPackageOfferingsQuery } from '@/api/package-offerings/get-package-offering-by-slp';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import { toaster } from '@/components/ui/toaster';
import { Colors } from '@/constants/colors';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { useQueryClient } from '@/lib/react-query';
import { Box, Text, useDisclosure } from '@chakra-ui/react';
import { useFormik } from 'formik';
import moment from 'moment';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { FormEvent, useEffect, useMemo, useState } from 'react';
import { FiPlusCircle } from 'react-icons/fi';

interface FilterParams {
  size?: number;
  currentPage?: number;
  organization_id?: string | undefined;
  user_id?: string | undefined;
}

type InitialValue = {
  client_id: number | null;
  display_name: string;
  [key: string]: any;
  package_offering: {
    id: number;
    [key: string]: any;
  } | null;
};

const initialValue: InitialValue = {
  client_id: null,
  display_name: '',
  package_offering: null,
  payment_method: '',
};

const generateLabel = (label: string) => {
  return (
    <Box display="flex" alignItems="center" gap="0.5rem">
      <FiPlusCircle
        fontWeight={'bold'}
        color={Colors?.ORANGE?.PRIMARY}
        size="1rem"
      />
      <Text
        color={Colors?.ORANGE?.PRIMARY}
        fontSize={'14px'}
        whiteSpace={'nowrap'}
        fontWeight={'bold'}
      >
        {label}
      </Text>
    </Box>
  );
};

export const useInvoiceItemHook = () => {
  const { UserFromQuery } = useSupabaseSession();
  const searchParams = useSearchParams();
  const router = useRouter();
  const taxDisclosure = useDisclosure();
  const [targetItem, setTargetItem] = useState<any>({});

  const orgIdFromUrl = searchParams.get('organization_id');
  const userIdFromUrl = searchParams.get('user_id');
  const { data: Slp } = useGetUserByIdQuery(Number(userIdFromUrl), {
    enabled: Boolean(Number(userIdFromUrl)),
  });

  const queryClient = useQueryClient();
  const params = useParams();
  const { mutateAsync: CreateInvoiceApi, isLoading: CreateInvoiceLoading } =
    useCreateInvoiceMutation();

  // Initialize filter state
  const [filter, setFilter] = useState<FilterParams>({
    size: 50,
    currentPage: 1,
    organization_id: orgIdFromUrl || UserFromQuery?.organization_id,
    user_id: userIdFromUrl || UserFromQuery?.id,
  });

  const [invoiceData, setInvoiceData] = useState({
    invoiceSubTotal: 0,
    total: 0,
    amountDue: 0,
    tax: 0,
    discount: 0,
  });

  const [discountData, setDiscountData] = useState<{
    type: 'percentage' | 'currency';
    value: number;
  }>({
    type: 'percentage',
    value: 0,
  });
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const itemDisclosure = useDisclosure();

  console.log('selectedItems is ', selectedItems);

  // Update filter when URL params or user data changes
  useEffect(() => {
    setFilter((prev) => ({
      ...prev,
      organization_id: Number(orgIdFromUrl) || UserFromQuery?.organization_id,
      user_id: Number(userIdFromUrl) || UserFromQuery?.id,
    }));
  }, [orgIdFromUrl, UserFromQuery, userIdFromUrl]);

  // Recalculate figures when discount changes
  useEffect(() => {
    if (selectedItems.length > 0) {
      updateFigures(selectedItems);
    }
  }, [discountData]);

  const { data: TaxData } = useGetAllTaxesQuery(
    {
      user_id: filter.user_id || '',
      org_id: filter.organization_id,
    },
    {
      enabled: !!filter.organization_id,
      generateItemId: true,
    }
  );
  const { data: PackageOfferings } = useGetPackageOfferingsQuery(
    {
      user_id: filter.user_id || '',
      organization_id: filter.organization_id,
    },
    {
      enabled: !!filter.user_id,
      generateItemId: true,
    }
  );

  const { data: ServicesData } = useGetServicesQuery(
    orgIdFromUrl || UserFromQuery?.organization_id,
    {
      enabled: !!UserFromQuery?.organization_id,
      generateItemId: true,
    }
  );
  console.log('ServicesData', ServicesData);

  const { data: Client } = useGetClientByIdQuery(Number(params?.client_id));

  const invoiceItemOptions = useMemo(() => {
    const serviceItemOptions =
      ServicesData?.services?.map((item: any) => ({
        label: `Service - ${item?.name}`,
        value: {
          ...item,
          type: 'service',
        },
      })) || [];

    const packageItemOptions =
      PackageOfferings?.map((item: any) => ({
        label: `Package - ${item?.name}`,
        value: {
          ...item,
          type: 'package',
        },
      })) || [];
    return [...serviceItemOptions, ...packageItemOptions];
  }, [ServicesData, PackageOfferings]);

  // Calculate tax for a single item based on its associated taxes
  // const calculateItemTax = (item: any) => {
  //   if (!item.taxes || item.taxes.length === 0) return 0;

  //   return item.taxes.reduce((total: number, tax: any) => {
  //     const taxAmount =
  //       (Number(tax.value) / 100) * Number(item.price) * Number(item.quantity);
  //     return total + taxAmount;
  //   }, 0);
  // };

  const calculateItemTax = (item: any) => {
    if (!Array.isArray(item.taxes) || item.taxes.length === 0) return 0;

    const subtotal = Number(item.price) * Number(item.quantity ?? 1);

    const totalTaxPercentage = item.taxes.reduce((sum: number, tax: any) => {
      const value = Number(tax?.value);
      return !isNaN(value) ? sum + value : sum;
    }, 0);

    return (totalTaxPercentage / 100) * subtotal;
  };

  const updateFigures = (newItems: any[]) => {
    console.log('newItems', newItems);
    const subTotal = newItems?.reduce((prev, item) => {
      return prev + item?.quantity * item?.price;
    }, 0);

    // Calculate discount
    let discountAmount = 0;
    if (discountData.value > 0) {
      if (discountData.type === 'percentage') {
        discountAmount = (Number(subTotal) * discountData.value) / 100;
      } else {
        discountAmount = discountData.value;
      }
    }

    const subTotalWithDiscount = subTotal - discountAmount;
    const totalTax = newItems?.reduce((prev, item) => {
      return prev + calculateItemTax(item);
    }, 0);
    console.log('totalTax is ', totalTax);

    console.log('subTotal', subTotal);
    const total = Number(subTotalWithDiscount) + Number(totalTax);
    const amountDue = total;
    setInvoiceData({
      amountDue: amountDue,
      invoiceSubTotal: subTotal,
      total: total,
      tax: totalTax,
      discount: discountAmount,
    });
  };

  // const updateFigures = (newItems: any[]) => {
  //   console.log('newItems', newItems);
  //   const subTotal = newItems?.reduce((prev, item) => {
  //     return prev + item?.quantity * item?.price;
  //   }, 0);

  //   const totalTax = newItems?.reduce((prev, item) => {
  //     return prev + calculateItemTax(item);
  //   }, 0);

  //   console.log('totalTax', totalTax);
  //   console.log('subTotal', subTotal);
  //   const total = subTotal + totalTax;
  //   const amountDue = total;
  //   setInvoiceData({
  //     amountDue: amountDue,
  //     invoiceSubTotal: subTotal,
  //     total: total,
  //     tax: totalTax,
  //   });
  // };

  const handleItemClick = (item: any) => {
    console.log('item is ', item);

    const itemFound = selectedItems?.find(
      (existingItem: any) => existingItem?.itemId == item?.itemId
    );
    if (itemFound) {
      const newItems = selectedItems?.map((existingItem: any) => {
        if (existingItem?.itemId == item?.itemId) {
          const quantity = itemFound?.quantity + 1;
          return {
            ...itemFound,
            quantity,
            amount: itemFound?.price * quantity,
          };
        }
        return existingItem;
      });
      updateFigures(newItems);
      return setSelectedItems(() => [...newItems]);
    }

    const newItems = [
      ...selectedItems,
      {
        ...item,
        quantity: 1,
        amount: item?.price,
        ...(item?.type === 'service'
          ? { service_id: item?.id }
          : { package_id: item?.id }),
      },
    ];
    updateFigures(newItems);
    setSelectedItems(() => {
      return [...newItems];
    });
  };

  const updateItem = (field: string, value: any, itemId: any) => {
    const newItems = selectedItems?.map((item: any) => {
      if (item?.itemId == itemId) {
        const data = {
          ...item,
          [field]: value,
        };
        return {
          ...data,
          amount:
            data?.quantity && data?.price
              ? data?.quantity * data?.price
              : data?.amount,
        };
      }
      return item;
    });
    updateFigures(newItems);
    setSelectedItems(() => [...newItems]);
  };

  const deleteItem = (itemId: any) => {
    const newItems = selectedItems?.filter((item) => {
      return item?.itemId != itemId;
    });
    updateFigures(newItems);
    setSelectedItems(() => [...newItems]);
  };

  const invoiceItemFormik = useFormik({
    initialValues: initialValue,
    onSubmit: async (values) => {
      try {
        if (!values.client_id) {
          throw new Error('Please select a client');
        }
        if (!values.package_offering) {
          throw new Error('Please select a package');
        }
        queryClient.invalidateQueries([
          queryKey.packages.getAllPurchasedPackage,
        ]);
        invoiceItemFormik.resetForm();
      } catch (error: any) {
        toaster.create({
          description: error?.message || ToastMessages.somethingWrong,
        });
      }
    },
  });

  const purchasePackage = async (e: FormEvent) => {
    e.preventDefault();
    await invoiceItemFormik.handleSubmit();
    ``;
  };

  const handleSelectClient = (client: any) => {
    invoiceItemFormik.setFieldValue('client_id', client?.id);
    invoiceItemFormik.setFieldValue('display_name', client?.display_name);
    invoiceItemFormik.setFieldValue('email', client?.email);
  };

  console.log('UserFromQuery', UserFromQuery);

  const handleCreateInvoice = async () => {
    const payload: any = {
      currency_code: Slp?.organization?.currency_code || 'USD',
      email: Client?.email,
      name: Client?.display_name,
      client_id: Client?.id,
      invoice_date: moment().format('YYYY-MM-DD'),
      organization_id: orgIdFromUrl || UserFromQuery?.organization_id,
      user_id: userIdFromUrl || UserFromQuery?.id,
      tax_value: invoiceData?.tax,
      total_price: invoiceData?.total,
      slp: `${Slp?.first_name || UserFromQuery?.first_name} ${Slp?.last_name || UserFromQuery?.last_name}`,
      discount: discountData,
    };

    payload.items = selectedItems.map((item: any) => {
      return {
        package_id: item?.package_id,
        service_id: item?.id,
        // Send all tax IDs for the item
        tax_ids: item?.taxes?.map((tax: any) => tax.id) || [],
        is_package: !(item?.type === 'service'),
        price: Number(item?.price),
        quantity: Number(item?.quantity),
        description: item?.description,
        product_name: item?.description,
      };
    });
    console.log('payload is ', payload);

    await CreateInvoiceApi(payload);
    setSelectedItems([]);
    setInvoiceData({
      invoiceSubTotal: 0,
      total: 0,
      amountDue: 0,
      tax: 0,
      discount: 0,
    });
    setDiscountData({ type: 'percentage', value: 0 });
    await queryClient.invalidateQueries({
      queryKey: [
        queryKey.invoices.getByClients,
        {
          id: Client?.id,
        },
      ],
    });
    await queryClient.invalidateQueries({
      queryKey: ['newsf-get-all-invoices'],
    });

    const newUrl = `/contacts/${Client?.id}?tab=invoices`;
    if (orgIdFromUrl) {
      router.push(`${newUrl}&organization_id=${orgIdFromUrl}`);
    } else {
      router.push(newUrl);
    }
  };

  const handleApplyDiscount = (discount: {
    type: 'percentage' | 'currency';
    value: number;
  }) => {
    setDiscountData(discount);
    // Trigger recalculation with current items
    updateFigures(selectedItems);
  };

  const discountDisclosure = useDisclosure();

  const taxOptions = TaxData?.data
    ?.map((item: any) => ({
      label: `${item?.name} ${item?.value}%`,
      value: { id: item?.id, value: item?.value },
    }))
    .concat({
      label: generateLabel('Create New Tax'),
      action: 'createNewTax',
      value: { id: Infinity, value: 0 },
    });

  const actions: { [key: string]: any } = {
    createNewTax: (data: any) => {
      taxDisclosure.onOpen();
      setTargetItem(data);
    },
  };
  return {
    filter,
    setFilter,
    purchasePackage,
    itemDisclosure,
    invoiceItemFormik,
    searchResult,
    setSearchResult,
    discountData,
    handleApplyDiscount,
    discountDisclosure,
    handleSelectClient,
    invoiceItemOptions,
    selectedItems,
    setSelectedItems,
    handleItemClick,
    invoiceData,
    deleteItem,
    updateItem,
    Client,
    handleCreateInvoice,
    CreateInvoiceLoading,
    calculateItemTax,
    actions,
    targetItem,
    taxOptions,
    taxDisclosure,
  };
};

export type TPurchasePackageHook = ReturnType<typeof useInvoiceItemHook>;
