import StringInput from '@/components/Input/StringInput';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { useGetSingleClientHook } from '@/hooks/receptionist/contacts/useGetSingleClientHook';
import { FullClient } from '@/shared/interface/clients';
import { Box, Center, Flex, useDisclosure } from '@chakra-ui/react';
import { FaEdit } from 'react-icons/fa';
import EditEmail from './EditEmail';
import MergeEmailModal from './merge-email-modal';
import { useState } from 'react';

export type GetSingleClientHookReturnType = ReturnType<
  typeof useGetSingleClientHook
>;

export default function Email({
  data,
  getClientHook,
}: {
  getClientHook: GetSingleClientHookReturnType;
  data: FullClient;
}) {
  const {
    hasMultiplePrimaryEmail,
    setPrimaryEmail,
    setPrimaryLoading,
    removeAsPrimary,
  } = getClientHook;
  const [emailToEdit, setEmailToEdit] = useState<null | any>(null);

  const mergeEmailDisclosure = useDisclosure();
  const editEmailDisclosure = useDisclosure();

  return (
    <div>
      {data.client_emails &&
        data.client_emails
          .sort((a: any, b: any) => b.is_primary_email - a.is_primary_email)
          .map((email) => {
            return (
              <Flex mt={2} alignItems={'center'} gap={4} key={email.id}>
                <StringInput
                  inputProps={{
                    id: 'email',
                    name: 'email',
                    type: 'email',
                    readOnly: true,
                    value: email.email,
                    disabled: true,
                    w: { base: 'fit-content', md: '24rem' },
                  }}
                />

                <Box
                  onClick={() => {
                    editEmailDisclosure.onOpen();
                    setEmailToEdit(email);
                  }}
                  rounded={'4.8px'}
                  fontSize={'16px'}
                  display={'flex'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  minW={'36px'}
                  w={'36px'}
                  maxW={'36px'}
                  minH={'36px'}
                  cursor={'pointer'}
                  bg={'#EDE7F6'}
                  color={'#5E35B1'}
                >
                  <FaEdit />
                </Box>

                {email.is_primary_email ? (
                  <Flex gap={4} alignItems={'center'}>
                    <Center
                      rounded={'full'}
                      bg={'green.50'}
                      fontWeight={'medium'}
                      w={{ base: 'full', md: '10rem' }}
                      border={'1px solid rgba(22,163,74,0.2)'}
                      color={'green.700'}
                      py={'.3rem'}
                    >
                      Primary
                    </Center>

                    {hasMultiplePrimaryEmail && (
                      <Button
                        bg={'gray'}
                        minW={{ base: 'fit-content', md: '10rem' }}
                        _hover={{ bg: 'gray' }}
                        onClick={() =>
                          removeAsPrimary(Number(data?.id), email.email)
                        }
                      >
                        Remove as primary
                      </Button>
                    )}
                  </Flex>
                ) : (
                  <Button
                    onClick={() =>
                      setPrimaryEmail(Number(data.id), email.email)
                    }
                    loading={
                      email.email === setPrimaryLoading.email &&
                      setPrimaryLoading.loading
                    }
                    minW={{ base: 'fit-content', md: '10rem' }}
                    bg={'primary.500'}
                  >
                    Set as Primary
                  </Button>
                )}
              </Flex>
            );
          })}

      <Box mt={'1rem'} display={'flex'} gap={2}>
        <Button
          onClick={mergeEmailDisclosure.onOpen}
          minW={{ base: 'fit-content', md: '10rem' }}
        >
          Add Email
        </Button>
      </Box>

      <CustomModal
        w={{ base: '90%', md: '40rem' }}
        open={mergeEmailDisclosure.open}
        onOpenChange={mergeEmailDisclosure.onClose}
      >
        <MergeEmailModal
          onClose={mergeEmailDisclosure.onClose}
          getClientHook={getClientHook}
          data={data}
        />
      </CustomModal>
      <CustomModal
        w={{ base: '90%', md: '40rem' }}
        open={editEmailDisclosure.open}
        onOpenChange={editEmailDisclosure.onClose}
      >
        <EditEmail
          onClose={editEmailDisclosure.onClose}
          data={data}
          email={emailToEdit}
        />
      </CustomModal>
    </div>
  );
}
