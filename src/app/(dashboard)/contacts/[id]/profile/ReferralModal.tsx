import SearchContact from '@/components/elements/search/SearchContact';
import StringInput from '@/components/Input/StringInput';
import { ToastMessages } from '@/constants/toast-messages';
import { useGetSingleClientHook } from '@/hooks/receptionist/contacts/useGetSingleClientHook';
import { FullClient } from '@/shared/interface/clients';
import {
  Box,
  Center,
  Stack,
  Text,
  useDisclosure,
  HStack,
} from '@chakra-ui/react';
import React, { FormEvent, useState } from 'react';
import { toaster } from '@/components/ui/toaster';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { useGetAllReferralsQuery } from '@/api/referrals/get-all-referrals';
import { useAddReferralMutation } from '@/api/referrals/useAddReferral';
import { FaPlus } from 'react-icons/fa';

type GetSingleClientHookReturnType = ReturnType<typeof useGetSingleClientHook>;
export default function AddReferralModal({
  data,
  getClientHook,
  section,
  variant = 1,
}: {
  getClientHook: GetSingleClientHookReturnType;
  data: FullClient;
  section?: any;
  variant?: number;
}) {
  const { onOpen, onClose, open } = useDisclosure();

  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [selectedClient, setSelectedClient] = useState<any>();
  const [loading, setLoading] = useState(false);
  const [switchToReferee, setSwitchToReferee] = useState(true);
  const queryClient = useQueryClient();
  const { data: existingReferral } = useGetAllReferralsQuery(
    switchToReferee ? data.id : selectedClient?.id,
    {
      enabled: Boolean(switchToReferee ? data.id : selectedClient?.id),
    }
  ) as any;
  const { mutateAsync } = useAddReferralMutation();
  // console.log('exist', existingReferral);
  // check and see if the selected client has already been referred(is a referee or referrer)
  const refereeId = switchToReferee ? data.id : selectedClient?.id;
  const isSelectedClientAReferee = existingReferral?.some(
    (item: any) =>
      item?.referee?.id === refereeId || item?.referrer?.id === refereeId
  );

  const addReferral = async (e: FormEvent<HTMLFormElement>) => {
    try {
      e.preventDefault();
      setLoading(true);
      if (isSelectedClientAReferee) {
        return toaster.create({
          description: 'This client has already been referred',
          type: 'error',
        });
      }
      if (!selectedClient?.id || !data.id || !refereeId) {
        return toaster.create({
          description: 'Cannot insert null value',
          type: 'error',
        });
      }
      if (selectedClient?.id === data.id) {
        return toaster.create({
          description: `Oops!! Clients can't refer themselves`,
          type: 'error',
        });
      }
      await mutateAsync({
        referee_id: switchToReferee ? data.id : selectedClient?.id,
        referrer_id: switchToReferee ? selectedClient?.id : data.id,
        credit: 1,
      });
      queryClient.invalidateQueries({
        queryKey: [queryKey.referrals.getAllReferrals, data?.id],
      });
      await getClientHook.refetch();
      setSelectedClient(null);
      setSearchResult([]);
      onClose();
    } catch (error: any) {
      console.log('error', error);
      // const message = error?.response.
      toaster.create({
        description: error?.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box width={'100%'} display={'flex'} justifyContent={'flex-end'}>
      {section === 'profile' ? (
        <Center
          cursor="pointer"
          onClick={onOpen}
          h="20px"
          w="20px"
          bg={variant === 1 ? 'primary.500' : undefined}
          rounded={variant === 1 ? undefined : 'full'}
          _hover={{ bg: 'gray.50' }}
        >
          <FaPlus
            size={variant === 1 ? 10 : 14}
            color={variant === 1 ? 'white' : 'black'}
          />
        </Center>
      ) : (
        <Text
          onClick={onOpen}
          // cursor={'pointer'}
          // color={'primary.500'}
          // fontWeight={600}
        >
          Add Referral
        </Text>
      )}

      <CustomModal
        w={'40rem'}
        onOpenChange={onClose}
        open={open}
        // headertext={'Add Referral'}
      >
        <Text textAlign={'center'} mt={2} fontWeight={'bold'}>
          Add Referral
        </Text>
        <Text fontSize={'.8rem'} textAlign={'center'}>
          Add a referral to your account
        </Text>
        <form onSubmit={addReferral}>
          <Stack gap={'2rem'} my={'2rem'}>
            {/* <Box my={'1rem'}>
              <Text
                fontSize={'1.2rem'}
                textAlign={'center'}
                fontWeight={'bold'}
              >
                {`Add Client ${data?.first_name} ${data?.last_name} Referred to the Platform`}
              </Text>
            </Box> */}

            {/* Tab-based Toggle */}
            <HStack gap={3}>
              <Text
                cursor="pointer"
                fontWeight={!switchToReferee ? 'bold' : 'normal'}
                borderBottom={'2px solid'}
                fontSize={'1rem'}
                borderColor={!switchToReferee ? 'primary.500' : 'transparent'}
                color={!switchToReferee ? 'primary.600' : 'gray.500'}
                onClick={() => setSwitchToReferee(false)}
                pb={1}
              >
                Add as Referrer
              </Text>
              <Text
                cursor="pointer"
                fontSize={'1rem'}
                fontWeight={switchToReferee ? 'bold' : 'normal'}
                borderBottom={'2px solid'}
                borderColor={switchToReferee ? 'primary.500' : 'transparent'}
                color={switchToReferee ? 'primary.600' : 'gray.500'}
                onClick={() => setSwitchToReferee(true)}
                pb={1}
              >
                Add as Referee
              </Text>
            </HStack>
            <StringInput
              inputProps={{
                name: 'client',
                readOnly: true,
                disabled: true,
                defaultValue: `${data?.first_name} ${data?.last_name}`,
              }}
              fieldProps={{
                label: `${!switchToReferee ? 'Referrer' : 'Referee'} Name`,
              }}
            />
            <Stack>
              <label className="font-medium text-gray-900">Lookup Client</label>
              <SearchContact
                setSearchResult={(e: any) => {
                  setSearchResult(e);
                }}
                searchResult={searchResult}
                selectExistingUser={setSelectedClient}
              />
            </Stack>

            {selectedClient && (
              <StringInput
                inputProps={{
                  name: 'client',
                  readOnly: true,
                  value: `${selectedClient?.first_name} ${selectedClient?.last_name}`,
                }}
                fieldProps={{
                  label: `${switchToReferee ? 'Referrer' : 'Referee'} Name`,
                }}
              />
            )}

            <Button bg={'primary.500'} loading={loading} type="submit">
              Add
            </Button>
          </Stack>
        </form>
      </CustomModal>
    </Box>
  );
}
