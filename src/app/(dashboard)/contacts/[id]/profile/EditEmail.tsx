/* eslint-disable react-hooks/exhaustive-deps */
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { queryKey } from '@/constants/query-key';
import { useEditEmailHook } from '@/hooks/useEditEmailHook';
import { FullClient } from '@/shared/interface/clients';
import { Flex, Text } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';

const EditEmail = ({
  onClose,
  data,
  email,
}: {
  onClose: any;
  data: FullClient;
  email: any;
}) => {
  const queryClient = useQueryClient();

  const {
    editEmailLoading,
    newEmail,
    setNewEmail,
    setEmailId,
    setCurrentEditEmail,
    updateClientEmail,
  } = useEditEmailHook();

  useEffect(() => {
    // Find the primary email when component mounts
    if (email) {
      setCurrentEditEmail(email.email);
      setNewEmail(email.email);
      setEmailId(email.id);
    }
  }, [email]);

  // useEffect(() => {
  //   // Find the primary email when component mounts
  //   if (data.client_emails?.length) {
  //     const primaryEmail = data.client_emails.find(
  //       (email) => email.is_primary_email
  //     );
  //     if (primaryEmail) {
  //       setCurrentEditEmail(primaryEmail.email); // Set current email
  //       setNewEmail(primaryEmail.email); // Set new email
  //       setEmailId(primaryEmail.id); // Set email ID
  //     }
  //   }
  // }, [data.client_emails, setEmailId, setNewEmail, setCurrentEditEmail]);

  const handleSubmit = async () => {
    const success = await updateClientEmail();
    if (success) {
      queryClient.invalidateQueries({
        queryKey: [queryKey.client.getById, data?.id],
      });
      onClose();

      // await getClientHook.getClientData(Number(data.id));
    }
  };

  return (
    <div>
      <Text mt={'2rem'} textAlign={'center'} fontWeight={600}>
        Edit Client Email
      </Text>
      <StringInput
        inputProps={{
          value: newEmail,
          onChange: (e) => setNewEmail(e.target.value),
          placeholder: 'Enter new email',
        }}
        fieldProps={{ label: 'Email' }}
      />
      <Flex justifyContent={'flex-end'} gap={4} mt={'1rem'}>
        <Button bg={'gray.200'} color={'white'} onClick={onClose} minW={'8rem'}>
          Cancel
        </Button>
        <Button
          bg={'primary.500'}
          onClick={handleSubmit}
          loading={editEmailLoading}
          minW={'8rem'}
        >
          Update
        </Button>
      </Flex>
    </div>
  );
};

export default EditEmail;
