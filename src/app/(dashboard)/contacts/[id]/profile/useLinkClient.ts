import { GetSingleClientHookReturnType } from './email';
import { createLinkedClient } from '@/api/linked_clients';
import { FullClient } from '@/shared/interface/clients';
import { useState } from 'react';
import { ToastMessages } from '@/constants/toast-messages';
import supabase from '@/lib/supabase/client';
import { tableNames } from '@/constants/table_names';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { useParams } from 'next/navigation';
import { useGetLinkedClientsQuery } from '@/api/linked_clients/get-linked-clients';
import { toaster } from '@/components/ui/toaster';

export const useLinkClient = ({
  data,
  linkClientDisclosure,
  getClientHook,
}: {
  getClientHook: GetSingleClientHookReturnType;
  data: FullClient;
  linkClientDisclosure: any;
}) => {
  const { id } = useParams();
  const { data: LinkedClients } = useGetLinkedClientsQuery(Number(id), {
    enabled: Boolean(id),
  });

  const queryClient = useQueryClient();
  const [form, setForm] = useState({
    firstName: '',
    lastName: '',
    addedClientId: '',
    relationship: '',
    province: '',
    stage: '',
    email: '',
  });

  const [isNewClient, setIsNewClient] = useState(true);

  const [loading, setLoading] = useState(false);
  const canProceed = isNewClient
    ? form.firstName && form.lastName && form.relationship
    : // form.email &&
      // form.province &&
      // form.stage
      form.firstName &&
      form.lastName &&
      form.relationship &&
      form.addedClientId;
  const [searchResult, setSearchResult] = useState<Array<any>>([]);

  const relationshipOptions = [
    { label: 'Spouse', value: 'Spouse' },
    { label: 'Friend', value: 'Friend' },
    { label: 'Child', value: 'Child' },
    { label: 'Others', value: 'Others' },
  ];
  // console.log("contact is ", contact);
  const handleSubmit = async (e: any) => {
    try {
      e.preventDefault();
      setLoading(true);
      if (isNewClient) {
        const newClient = await createNewClient(form);
        await createLinkedClient({
          relationship: form.relationship,
          client_id: data.id,
          added_client_id: newClient.id,
        });
        toaster.create({
          description: ToastMessages.operationSuccess,
          type: 'success',
        });
        linkClientDisclosure.onClose();
        return;
      }

      if (
        !form.firstName ||
        !form.lastName ||
        !form.relationship ||
        !form.addedClientId
      ) {
        throw new Error('Please input all fields');
      }
      if (Number(form.addedClientId) === Number(id)) {
        throw new Error('You cannot link a client to the same client');
      }

      const allAddedClientId = LinkedClients?.map(
        (item: any) => item.added_client_id
      );
      if (allAddedClientId.includes(form.addedClientId)) {
        throw new Error('You cannot add the same client twice');
      }

      await createLinkedClient({
        relationship: form.relationship,
        client_id: data.id,
        added_client_id: form.addedClientId,
      });
      await getClientHook.refetch();
      await queryClient.invalidateQueries({
        queryKey: [queryKey.linkedClients.getByClientId, data.id],
      });
      linkClientDisclosure.onClose();
      toaster.create({
        description: ToastMessages.operationSuccess,
        type: 'success',
      });
    } catch (error: any) {
      toaster.create({
        description: error.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };
  const createNewClient = async (formData: typeof form) => {
    // Check if phone or email is provided
    if (formData.email !== '') {
      const query = supabase
        .from(tableNames.clients)
        .select('*')
        .eq('email', formData.email);

      const { data: existingData, error: existingError } = await query;

      if (existingError) {
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });

        return;
      }

      if (existingData.length > 0) {
        toaster.create({
          description: 'Email already exists.',
          type: 'error',
        });

        return;
      }
    }

    const { data: NewClient, error } = await supabase
      .from(tableNames.clients)
      .insert({
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        stage: data.stage,
        province: data.province,
        lead_created: new Date().toISOString(),
      })
      .select();

    if (error) throw error;

    if (NewClient[0]?.email !== '') {
      const insertClientEmail = {
        client_id: NewClient[0]?.id,
        email: NewClient[0]?.email,
      };
      const { error: emailError } = await supabase
        .from(tableNames.client_emails)
        .insert(insertClientEmail);

      if (emailError) {
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        throw emailError;
      }
    }
    await getClientHook.refetch();
    await queryClient.invalidateQueries({
      queryKey: [queryKey.linkedClients.getByClientId, data.id],
    });

    return NewClient?.[0];
  };

  const selectExistingUser = (e: any) => {
    console.log('existing ', e);
    setForm({
      ...form,
      firstName: e.first_name,
      lastName: e.last_name,
      addedClientId: e.id,
    });
  };

  return {
    selectExistingUser,
    form,
    setForm,
    loading,
    handleSubmit,
    relationshipOptions,
    searchResult,
    setSearchResult,
    canProceed,
    isNewClient,
    setIsNewClient,
  };
};
