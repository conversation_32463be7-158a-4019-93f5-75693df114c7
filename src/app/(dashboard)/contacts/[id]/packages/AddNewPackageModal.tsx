import { Box, useDisclosure, Text, Stack, Flex } from '@chakra-ui/react';
import StringInput from '@/components/Input/StringInput';
import CustomSelect from '@/components/Input/CustomSelect';
import SearchContact from '@/components/elements/search/SearchContact';
// import { productOptions } from '@/data/options/product';
import { FiDollarSign } from 'react-icons/fi';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { useGetAllProductsQuery } from '@/api/products/get-all-products';
import { IIProduct } from '@/shared/interface/products';
import { useAddNewPackageHook } from '@/hooks/packages/useAddNewPackageHook';
import {
  invoiceStatusOptions,
  sessionDuration,
  sourceOptions,
} from '@/data/options';
import CTABtn from '@/components/elements/CTABtn';

export default function AddNewPackageModal({
  data,
  createNewPackage,
}: {
  data?: any;
  createNewPackage?: any;
}) {
  const { data: productData } = useGetAllProductsQuery();
  const productOptions = productData?.allProducts?.map(
    (currentProduct: IIProduct) => {
      return {
        label: currentProduct.name,
        value: currentProduct.name,
      };
    }
  );
  const { open, onClose, onOpen } = useDisclosure();
  const {
    loading,
    setFieldValue,
    values,
    errors,
    touched,
    searchResult,
    setSearchResult,
    setShowClients,
    handleSearchSelect,
    showClients,
    handleSubmit,
    handleChange,
  } = useAddNewPackageHook(data, onClose);

  const handleOpenModal = () => {
    onOpen();
    setShowClients(false);
  };

  return (
    <div>
      <Box>
        {/* <Button bg={'primary.500'} onClick={handleOpenModal} mt={'1.5rem'}>
          Create Package
        </Button> */}
        <CTABtn
          onClick={handleOpenModal}
          variant={2}
          buttonName="New Package"
        />
      </Box>

      <CustomModal
        w={{ base: '90%', md: '40rem' }}
        open={open}
        onOpenChange={onClose}
      >
        <Box my={'1rem'}>
          <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
            Create a New Package
          </Text>
        </Box>

        <form onSubmit={handleSubmit}>
          <Stack gap={'1rem'} pt={'1rem'}>
            {createNewPackage === 'raw' && !showClients && (
              <Stack>
                <label className="font-medium text-gray-900">
                  Lookup Client
                </label>
                <SearchContact
                  setSearchResult={(e: any) => {
                    setSearchResult(e);
                  }}
                  searchResult={searchResult}
                  selectExistingUser={(item) => {
                    handleSearchSelect(item.id);
                    console.log(item);
                  }}
                />
              </Stack>
            )}
            {createNewPackage === 'raw' && showClients && (
              <StringInput
                fieldProps={{
                  invalid: touched.name && !!errors.name,
                  label: '  Client Name',
                  required: true,
                }}
                inputProps={{
                  name: 'name',
                  value: values.name?.trim() || '',
                  onChange: handleChange,
                  readOnly: true,
                  disabled: true,
                }}
              />
            )}
            {/* <CustomSelect
              placeholder="Select Package Type"
              onChange={(val) => setFieldValue('product', val?.value)}
              options={packageTypeOptions}
              required={true}
              label="Select Package Ty[e"
            /> */}
            <CustomSelect
              placeholder="Select Product"
              onChange={(val) => setFieldValue('product', val?.value)}
              options={productOptions}
              required={true}
              label="Product"
            />
            {/* <StringInput
              formControlProps={{
                isInvalid: touched.product && !!errors.product,
                label: '  Product',
                // isRequired: true,
              }}
              inputProps={{
                name: 'product',
                value: values.product?.trim() || '',
                onChange: handleChange,
              }}
              error={!!errors.product}
              touched={touched.product}
              errorMessage={errors.product}
            /> */}
            <CustomSelect
              placeholder="Select Status"
              onChange={(val) => setFieldValue('status', val?.value)}
              options={invoiceStatusOptions}
              required={true}
              label="Status"
            />
            <CustomSelect
              placeholder="Select Source"
              onChange={(val) => setFieldValue('source', val?.value)}
              options={sourceOptions}
              required={true}
              label="Source"
            />
            <CustomSelect
              placeholder="Select Duration"
              onChange={(val) => setFieldValue('session_duration', val?.value)}
              options={sessionDuration}
              required={true}
              label="Duration (Minutes)"
            />
            <StringInput
              fieldProps={{
                invalid: touched.price && !!errors.price,
                label: 'Price',
                errorText: errors?.price,
                required: true,
                // isRequired: true,
              }}
              inputProps={{
                name: 'price',
                value: values.price?.trim() || undefined,
                onChange: handleChange,
              }}
              inputGroup={{ startElement: <FiDollarSign />, w: 'full' }}
            />

            <StringInput
              fieldProps={{
                invalid: touched.balance && !!errors.balance,
                label: 'Balance',
                required: true,
                errorText: errors?.balance,
              }}
              inputProps={{
                name: 'balance',
                value: values.balance?.trim() || undefined,
                onChange: handleChange,
              }}
            />

            <StringInput
              fieldProps={{
                invalid: touched.package_size && !!errors.package_size,
                label: 'Package Size',
                required: true,
                errorText: errors.package_size,
              }}
              inputProps={{
                name: 'package_size',
                value: values.package_size?.trim() || undefined,
                onChange: handleChange,
              }}
            />

            <StringInput
              inputProps={{
                type: 'datetime-local',

                name: 'expiry_date',
                value: values.expiry_date || undefined,
                onChange: handleChange,
              }}
              fieldProps={{
                label: 'Expiry Date',
              }}
            />

            <Flex
              my={'1.8rem'}
              alignItems={'center'}
              justifyContent={'space-between'}
            >
              <Button
                onClick={onClose}
                variant={'outline'}
                minH={'3rem'}
                minW={'15rem'}
              >
                Cancel{' '}
              </Button>
              <Button
                loading={loading}
                minH={'3rem'}
                minW={'15rem'}
                bg={'primary.500'}
                type="submit"
              >
                Save
              </Button>
            </Flex>
          </Stack>
        </form>
      </CustomModal>
    </div>
  );
}
