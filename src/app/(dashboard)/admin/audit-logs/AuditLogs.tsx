'use client';
import { useGetAllAuditLogsQuery } from '@/api/admin/query';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import { Box, Center, Heading } from '@chakra-ui/react';
import { columnDef } from './columnDef';

export default function AuditLogs() {
  const { data, isLoading } = useGetAllAuditLogsQuery();
  console.log('data is ', data);

  return (
    <div>
      <Heading
        fontSize={{ base: '1.3rem', md: '2rem' }}
        fontWeight={'semibold'}
        mb={'4'}
      >
        Audit Logs
      </Heading>

      <Box minH={'20rem'} mt={'2rem'}>
        {isLoading ? (
          <Center pt={'20'}>
            <AnimateLoader />
          </Center>
        ) : (
          <CustomTable columnDef={columnDef} data={data?.data || []} />
        )}
      </Box>
    </div>
  );
}
