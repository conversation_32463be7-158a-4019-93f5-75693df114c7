import {
  <PERSON>u<PERSON>ontent,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>uR<PERSON>,
  MenuTrigger,
} from '@/components/ui/menu';
import { Tooltip } from '@/components/ui/tooltip';
import { getSlugFromName } from '@/utils/event';
import {
  Badge,
  Box,
  Button,
  Card,
  Center,
  Grid,
  Heading,
  HStack,
  Icon,
  IconButton,
  Input,
  Tabs,
  Text,
  Textarea,
  VStack,
} from '@chakra-ui/react';
import { memo, useCallback } from 'react';
import {
  FiBarChart,
  FiFileText,
  FiInfo,
  FiLayers,
  FiLink,
  FiPlus,
  FiUser,
  FiX,
} from 'react-icons/fi';
// Define the props interface for SidebarContent
interface SidebarContentProps {
  activeTab: number;
  setActiveTab: (tab: number) => void;
  formik: any; // You can replace with proper Formik type if needed
  formErrors: {
    // title: boolean;
    // url: boolean;
    qts: string[];
  };
  org: any;
  env: any;
  qtTypes: any[];
  addQuestion: (type: string) => void;
  addSection: () => void;
  sidebarOpen: boolean;
  onSidebarClose: () => void;
  pages: any[];
  currentPageIndex: number;
  setCurrentPageIndex: (index: number) => void;
  totalQuestions: number;
  totalSections: number;
  syncedFields: any;
  applySyncedFields: (fields: any) => void;
  isQuestion: (item: any) => boolean;
  isSection: (item: any) => boolean;
  addPage: () => void;
  deletePage: (pageIndex: number) => void;
}

// Extract SidebarContent as a separate memoized component
const FormSidebarContent = memo<SidebarContentProps>(
  ({
    activeTab,
    setActiveTab,
    formik,
    org,
    env,
    qtTypes,
    addQuestion,
    addSection,
    sidebarOpen,
    onSidebarClose,
    pages,
    currentPageIndex,
    setCurrentPageIndex,
    totalQuestions,
    totalSections,
    syncedFields,
    applySyncedFields,
    isQuestion,
    isSection,
    addPage,
    deletePage,
  }) => {
    // Memoize handlers to prevent unnecessary re-renders
    const handleTitleChange = useCallback(
      (e: any) => {
        formik.setFieldValue('title', e.target.value);
        formik.setFieldValue('slug', getSlugFromName(e.target.value));
      },
      [formik]
    );

    const handleSlugChange = useCallback(
      (e: any) => {
        formik.handleChange(e);
      },
      [formik]
    );

    const handleDescriptionChange = useCallback(
      (e: any) => {
        formik.handleChange(e);
      },
      [formik]
    );

    return (
      <VStack h="full" align="stretch" spaceY={0}>
        <Tabs.Root
          borderColor={'primary.500'}
          value={activeTab.toString()}
          onValueChange={(details) => setActiveTab(parseInt(details.value))}
          h="full"
          display="flex"
          flexDirection="column"
        >
          <Tabs.List px={4} mt={4} flexShrink={0}>
            <Tabs.Trigger value="0">Builder</Tabs.Trigger>
            <Tabs.Trigger value="1">Sync Fields</Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content
            value="0"
            flex={1}
            px={4}
            pb={4}
            overflowY="auto"
            maxH="calc(100vh - 120px)"
          >
            <VStack spaceY={4} mt={4}>
              {/* Form Configuration */}
              <Card.Root borderColor={'gray.50'} minW={'100%'}>
                <Card.Header pb={3}>
                  <Heading size="sm">
                    <HStack>
                      <FiFileText />
                      <Text>Form Configuration</Text>
                    </HStack>
                  </Heading>
                </Card.Header>
                <Card.Body spaceY={4} minW={'100%'}>
                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={1}>
                      Form Title{' '}
                      <Text as="span" color="red.500">
                        *
                      </Text>
                    </Text>
                    <Input
                      name="title"
                      value={formik.values.title}
                      onChange={handleTitleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter form title"
                      required
                      size="sm"
                      borderColor={formik.touched.title && formik.errors.title}
                    />
                    {formik.touched.title && formik.errors.title && (
                      <Text fontSize="xs" color="red.500" mt={1}>
                        {formik.errors.title || ''}
                      </Text>
                    )}
                  </Box>

                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={1}>
                      Form URL{' '}
                      <Text as="span" color="red.500">
                        *
                      </Text>
                    </Text>
                    <HStack spaceX={1} align="center">
                      <Tooltip
                        content={`${env.FRONTEND_URL}/forms/${org?.slug}/${formik.values.slug || 'your-form-slug'}`}
                        aria-label="Full form URL"
                        interactive
                        contentProps={{ css: { '--tooltip-bg': 'gray' } }}
                      >
                        <Center
                          cursor="pointer"
                          color="gray.500"
                          _hover={{ color: 'primary.500' }}
                          p={1}
                        >
                          <FiLink size={18} />
                          {/* <Icon as={FiLink} boxSize={4} /> */}
                        </Center>
                      </Tooltip>
                      <Input
                        name="slug"
                        value={formik.values.slug}
                        onChange={handleSlugChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter form URL"
                        flex={1}
                        required
                        size="sm"
                        borderColor={
                          formik.touched.slug && formik.errors.slug
                            ? 'red.300'
                            : 'gray.50'
                        }
                      />
                    </HStack>
                    {formik.touched.slug && formik.errors.slug && (
                      <Text fontSize="xs" color="red.500" mt={1}>
                        {formik.errors.slug}
                      </Text>
                    )}
                  </Box>
                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={1}>
                      Form Description
                    </Text>
                    <Textarea
                      borderColor="gray.50"
                      name="description"
                      value={formik.values.description}
                      onChange={handleDescriptionChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter form description (optional)"
                      rows={2}
                      size="sm"
                    />
                  </Box>
                </Card.Body>
              </Card.Root>

              {/* Add Items - Rest of your existing content */}
              <Card.Root borderColor={'gray.50'} minW={'100%'}>
                <Card.Header pb={3}>
                  <Heading size="sm">
                    <HStack>
                      <FiPlus />
                      <Text>Add Items</Text>
                    </HStack>
                  </Heading>
                </Card.Header>
                <Card.Body spaceY={3} minW={'100%'}>
                  <MenuRoot>
                    <MenuTrigger asChild minWidth={'100%'}>
                      <Button
                        w="full"
                        _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                        variant="outline"
                        minWidth={'100%'}
                      >
                        Select Question Type
                      </Button>
                    </MenuTrigger>
                    <MenuContent zIndex={9999} minWidth={'100%'}>
                      {qtTypes.map((qtType) => (
                        <MenuItem
                          minWidth={'100%'}
                          cursor="pointer"
                          _hover={{ bg: 'primary.100' }}
                          key={qtType.type}
                          value={qtType.type}
                          onClick={() => {
                            addQuestion(qtType.type);
                            if (sidebarOpen) {
                              onSidebarClose();
                            }
                          }}
                        >
                          <HStack minWidth={'100%'}>
                            <qtType.icon />
                            <VStack align="start" spaceY={0}>
                              <Text fontWeight="medium">{qtType.label}</Text>
                            </VStack>
                          </HStack>
                        </MenuItem>
                      ))}
                    </MenuContent>
                  </MenuRoot>
                  <Button
                    _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                    onClick={() => {
                      addSection();
                      if (sidebarOpen) {
                        onSidebarClose();
                      }
                    }}
                    w="full"
                    variant="outline"
                  >
                    <HStack>
                      <FiLayers />
                      <Text>Add Section Divider</Text>
                    </HStack>
                  </Button>
                  <Text fontSize="xs" color="gray.500">
                    Add a section header to organize your form
                  </Text>
                </Card.Body>
              </Card.Root>

              {/* Pages */}
              <Card.Root borderColor={'gray.50'} minW={'100%'}>
                <Card.Header pb={3}>
                  <Heading size="sm">Pages ({pages.length})</Heading>
                </Card.Header>
                <Card.Body minW={'100%'}>
                  <VStack spaceY={2} minW={'100%'}>
                    {pages.map((page, index) => (
                      <Box
                        key={page.id}
                        p={3}
                        rounded="lg"
                        border="1px"
                        cursor="pointer"
                        w="full"
                        bg={
                          index === currentPageIndex ? 'primary.50' : 'gray.50'
                        }
                        borderColor={
                          index === currentPageIndex
                            ? 'primary.200'
                            : 'gray.200'
                        }
                        _hover={{
                          bg:
                            index === currentPageIndex
                              ? 'primary.50'
                              : 'gray.100',
                        }}
                      >
                        <HStack justify="between" minW={'100%'}>
                          <VStack
                            align="start"
                            spaceY={0}
                            onClick={() => setCurrentPageIndex(index)}
                            flex={1}
                          >
                            <Text fontWeight="medium" fontSize="sm">
                              {page.title}
                            </Text>
                            <Text fontSize="xs" color="gray.500">
                              {
                                page.items.filter((item: any) =>
                                  isQuestion(item)
                                ).length
                              }{' '}
                              questions,{' '}
                              {
                                page.items.filter((item: any) =>
                                  isSection(item)
                                ).length
                              }{' '}
                              sections
                            </Text>
                          </VStack>
                          <HStack>
                            <Badge fontSize="xs">Page {index + 1}</Badge>
                            {index !== 0 && pages.length > 1 && (
                              <IconButton
                                size="sm"
                                variant="ghost"
                                _hover={{
                                  bg: 'primary.100',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deletePage(index);
                                }}
                                colorScheme="red"
                              >
                                <FiX />
                              </IconButton>
                            )}
                          </HStack>
                        </HStack>
                      </Box>
                    ))}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={addPage}
                      w="full"
                      _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                    >
                      <HStack>
                        <FiPlus />
                        <Text>Add Page</Text>
                      </HStack>
                    </Button>
                  </VStack>
                </Card.Body>
              </Card.Root>

              {/* Form Summary */}
              <Card.Root borderColor={'gray.50'}>
                <Card.Header pb={3}>
                  <Heading size="sm">
                    <HStack>
                      <FiBarChart />
                      <Text>Form Summary</Text>
                    </HStack>
                  </Heading>
                </Card.Header>
                <Card.Body>
                  <Grid templateColumns="repeat(3, 1fr)" gap={3}>
                    <VStack>
                      <Text fontSize="xl" fontWeight="bold" color="blue.600">
                        {pages.length}
                      </Text>
                      <Text
                        fontSize="xs"
                        color="gray.500"
                        textAlign="center"
                        whiteSpace="nowrap"
                      >
                        Total Pages
                      </Text>
                    </VStack>
                    <VStack>
                      <Text fontSize="xl" fontWeight="bold" color="green.600">
                        {totalQuestions}
                      </Text>
                      <Text
                        fontSize="xs"
                        color="gray.500"
                        textAlign="center"
                        whiteSpace="nowrap"
                      >
                        Total Questions
                      </Text>
                    </VStack>
                    <VStack>
                      <Text fontSize="xl" fontWeight="bold" color="purple.600">
                        {totalSections}
                      </Text>
                      <Text
                        fontSize="xs"
                        color="gray.500"
                        textAlign="center"
                        whiteSpace="nowrap"
                      >
                        Total Sections
                      </Text>
                    </VStack>
                  </Grid>
                </Card.Body>
              </Card.Root>
            </VStack>
          </Tabs.Content>

          <Tabs.Content
            value="1"
            flex={1}
            px={4}
            pb={4}
            overflowY="auto"
            maxH="calc(100vh - 120px)"
          >
            <VStack spaceY={4} mt={4}>
              {/* Descriptive Header Section */}
              <Box
                w="full"
                p={4}
                bg="primary.50"
                borderRadius="md"
                border="1px solid"
                borderColor="primary.200"
              >
                <VStack spaceY={2} align="start">
                  <HStack>
                    <FiUser color="primary.600" size={20} />
                    <Text
                      fontWeight="semibold"
                      fontSize="md"
                      color="primary.800"
                    >
                      Synced Fields
                    </Text>
                  </HStack>
                  <Text fontSize="xs" color="primary.700" lineHeight="1.5">
                    These are special fields that automatically sync with user
                    profiles. When a user fills out a form with synced fields,
                    their responses are saved to their profile and can be
                    auto-populated in future forms. Perfect for collecting
                    consistent user data across multiple forms.
                  </Text>
                  {/* <HStack fontSize="xs" color="blue.600" mt={1}>
                    <Text fontWeight="medium">Benefits:</Text>
                    <Text>• Auto-populate data</Text>
                    <Text>• Consistent user tracking</Text>
                    <Text>• Reduced form completion time</Text>
                  </HStack> */}
                </VStack>
              </Box>

              {/* Synced Fields List */}
              <VStack spaceY={3} w="full">
                {/* <Text fontSize="sm" color="gray.600" alignSelf="start">
                  Available synced fields for user profile management
                </Text> */}

                {syncedFields.map((field: any) => (
                  <Card.Root
                    key={field.id}
                    cursor="pointer"
                    _hover={{
                      shadow: 'md',
                      borderColor: 'gray.50',
                      transform: 'translateY(-1px)',
                      transition: 'all 0.2s ease-in-out',
                    }}
                    w="full"
                    border="1px solid"
                    borderColor="gray.50"
                    transition="all 0.2s ease-in-out"
                  >
                    <Card.Body p={4}>
                      <HStack align="start" spaceX={3}>
                        {/* Field Icon */}
                        <Box
                          p={2}
                          bg="primary.50"
                          borderRadius="md"
                          flexShrink={0}
                        >
                          <field.icon color="primary.600" size={18} />
                        </Box>

                        {/* Field Content */}
                        <VStack align="start" flex={1} spaceY={2}>
                          <VStack align="start" spaceY={1}>
                            <HStack justify="between" w="full">
                              <Text
                                fontWeight="semibold"
                                fontSize="sm"
                                color="gray.800"
                              >
                                {field.label}
                              </Text>
                              {/* <HStack spaceX={1}>
                                <FiUser size={12} />
                                <Text
                                  fontSize="xs"
                                  color="blue.600"
                                  fontWeight="medium"
                                >
                                  Synced
                                </Text>
                              </HStack> */}
                            </HStack>

                            <Text fontSize="xs" color="gray.500">
                              {field.description}
                            </Text>

                            {/* Field Type Badge */}
                            <HStack spaceX={2}>
                              <Badge
                                size="sm"
                                colorScheme="gray"
                                variant="subtle"
                                px={2}
                                py={1}
                                fontSize="xs"
                              >
                                {field.type === 'Textbox'
                                  ? 'Short Text'
                                  : field.type === 'TextArea'
                                    ? 'Long Text'
                                    : field.type === 'Date'
                                      ? 'Date Picker'
                                      : field.type === 'phone'
                                        ? 'Phone Number'
                                        : field.type}
                              </Badge>

                              {field.required && (
                                <Badge
                                  size="sm"
                                  colorScheme="red"
                                  variant="subtle"
                                  px={2}
                                  py={1}
                                  fontSize="xs"
                                >
                                  Required
                                </Badge>
                              )}
                            </HStack>
                          </VStack>

                          {/* Add Button */}
                          <Button
                            size="sm"
                            variant="solid"
                            bg="primary.500"
                            color="white"
                            _hover={{
                              transform: 'scale(1.02)',
                              transition: 'transform 0.1s ease-in-out',
                            }}
                            onClick={() => {
                              applySyncedFields(field);
                              if (sidebarOpen) {
                                onSidebarClose();
                              }
                            }}
                          >
                            <FiPlus size={14} /> Add Field
                          </Button>
                        </VStack>
                      </HStack>
                    </Card.Body>
                  </Card.Root>
                ))}
              </VStack>

              {/* Footer Info */}
              <Box w="full" p={3} bg="gray.50" borderRadius="md" mt={4}>
                <HStack spaceX={2} fontSize="xs" color="gray.600">
                  <Icon as={FiInfo} />
                  <Text>
                    Tip: Synced fields will automatically populate with existing
                    user data when they fill out forms, making the experience
                    faster and more convenient.
                  </Text>
                </HStack>
              </Box>
            </VStack>
          </Tabs.Content>
        </Tabs.Root>
      </VStack>
    );
  }
);

FormSidebarContent.displayName = 'FormSidebarContent';

export default FormSidebarContent;
