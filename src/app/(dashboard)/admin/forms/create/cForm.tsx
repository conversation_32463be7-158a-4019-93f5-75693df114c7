'use client';

import { eventOptions } from '@/app/(dashboard)/contacts/[id]/notableDate/_hooks/shared';
import CustomSelect from '@/components/Input/CustomSelect';

import AnimateLoader from '@/components/elements/loader/animate-loader';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DrawerBackdrop,
  DrawerContent,
  DrawerHeader,
  DrawerRoot,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Radio, RadioGroup } from '@/components/ui/radio';
import { Switch } from '@/components/ui/switch';
import { env } from '@/constants/env';
import { useNewFormHook } from '@/hooks/forms/useNewFormHook';
import {
  Badge,
  Box,
  Button,
  Card,
  Center,
  Container,
  Flex,
  Heading,
  HStack,
  IconButton,
  Input,
  Stack,
  Text,
  Textarea,
  VStack,
} from '@chakra-ui/react';
import {
  FiChevronLeft,
  FiChevronRight,
  FiCopy,
  FiFileText,
  FiLayers,
  FiLock,
  FiMenu,
  FiMove,
  FiPlus,
  FiSave,
  FiTrash2,
  FiX,
} from 'react-icons/fi';
import { LiaTimesSolid } from 'react-icons/lia';
import FormSidebarContent from './FormSidebarContent';

const CForm = ({ slp, getFormsViewState, id }: any) => {
  const {
    formik,
    loading,
    pages,
    currentPage,
    currentPageIndex,
    setCurrentPageIndex,
    selectedItem,
    setSelectedItem,
    activeTab,
    setActiveTab,
    formErrors,
    totalSections,
    totalQuestions,
    sidebarOpen,
    onSidebarOpen,
    onSidebarClose,
    draggedItem,
    dropZoneIndex,
    handleDragStart,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    addPage,
    deletePage,
    FormData,
    addQuestion,
    addSection,
    org,
    FormDataIsLoading,
    updateItem,
    deleteItem,
    moveItem,
    applySyncedFields,
    saveForm,
    qtTypes,
    syncedFields,
    isQuestion,
    isSection,
  } = useNewFormHook({ slp, id });

  console.log('FormData', FormData);
  console.log('formErrors', formErrors);
  console.log('selectedItem', selectedItem);
  console.log('currentPage', currentPage);

  // Helper function to handle notable date toggle for date questions
  const handleNotableDateToggle = (itemId: string, currentValue: boolean) => {
    const newNotableDate = !currentValue;

    // Update the item with notable date and automatically set required to true if notable date is enabled
    const updates: any = {
      notableDate: newNotableDate,
    };

    // Automatically set required to true when notable date is checked
    if (newNotableDate) {
      updates.required = true;
    }

    updateItem(itemId, updates);
  };

  // Helper function to render disabled question input preview
  const renderQuestionPreview = (item: any) => {
    switch (item.type) {
      case 'Textbox':
        return (
          <Input
            placeholder="Text input preview"
            disabled
            borderColor="gray.200"
            bg="gray.50"
            size="sm"
            w="100%"
            maxW="100%"
            mx="auto"
            p={2}
            borderRadius="md"
          />
        );
      case 'TextArea':
        return (
          <Textarea
            placeholder="Long text input preview"
            disabled
            borderColor="gray.200"
            bg="gray.50"
            size="sm"
            rows={3}
            w="100%"
            maxW="100%"
            mx="auto"
            p={2}
            borderRadius="md"
          />
        );
      case 'Number':
        return (
          <Input
            type="number"
            placeholder="Number input preview"
            disabled
            borderColor="gray.200"
            bg="gray.50"
            size="sm"
            w="100%"
            maxW="100%"
            mx="auto"
            p={2}
            borderRadius="md"
          />
        );
      case 'Email':
        return (
          <Input
            type="email"
            placeholder="Email input preview"
            disabled
            borderColor="gray.200"
            bg="gray.50"
            size="sm"
            w="100%"
            maxW="100%"
            mx="auto"
            p={2}
            borderRadius="md"
          />
        );
      case 'Phone':
        return (
          <Input
            type="tel"
            placeholder="Phone input preview"
            disabled
            borderColor="gray.200"
            bg="gray.50"
            size="sm"
            w="100%"
            maxW="100%"
            mx="auto"
            p={2}
            borderRadius="md"
          />
        );
      case 'Date':
        return (
          <Input
            type="date"
            disabled
            borderColor="gray.200"
            bg="gray.50"
            size="sm"
            w="100%"
            maxW="100%"
            mx="auto"
            p={2}
            borderRadius="md"
          />
        );
      case 'Single choice':
        return (
          <RadioGroup disabled value="">
            <Stack gap={'.2rem'} w="100%">
              {(item.options || ['Option 1', 'Option 2']).map(
                (option: any, index: any) => (
                  <Radio
                    key={index}
                    disabled
                    colorScheme="primary"
                    value={option}
                  >
                    {option}
                  </Radio>
                )
              )}
            </Stack>
          </RadioGroup>
        );
      case 'Multi choice':
        return (
          <Stack gap={'.2rem'} w="100%">
            {(item.options || ['Option 1', 'Option 2']).map(
              (option: any, index: any) => (
                <Checkbox key={index} disabled colorScheme="primary">
                  {option}
                </Checkbox>
              )
            )}
          </Stack>
        );
      default:
        return (
          <Input
            placeholder="Input preview"
            disabled
            borderColor="gray.200"
            bg="gray.50"
            size="sm"
            w="100%"
            maxW="100%"
            mx="auto"
            p={2}
            borderRadius="md"
          />
        );
    }
  };

  if (FormDataIsLoading && id) {
    return (
      <Center h={'20rem'}>
        <AnimateLoader />
      </Center>
    );
  }

  return (
    <Box minH="100vh" bg="gray.50">
      {/* Header */}
      <Box bg="white" borderBottom="1px" borderColor="gray.200" py={4} px={0}>
        <HStack justify="between">
          <Box
            onClick={() => getFormsViewState('dashboard')}
            borderRadius={'10px'}
            p={'10px'}
            cursor={'pointer'}
            _hover={{ bg: '#d96847', color: '#fff' }}
            bg={'#F2F2F2'}
            flexShrink={0}
          >
            <LiaTimesSolid />
          </Box>
          <HStack>
            {/* <FiFileText color="blue.600" size={'15px'} /> */}
            <VStack align="start" spaceY={0}>
              <Heading
                size="lg"
                color="gray.900"
                fontSize={{ base: '15px', md: '19px' }}
                textTransform={'capitalize'}
              >
                {FormData?.title || 'Untitled Form'}
              </Heading>
              <Text
                fontSize="sm"
                color="gray.500"
                display={{ base: 'none', sm: 'block' }}
              >
                Create and customize therapy forms
              </Text>
            </VStack>
          </HStack>
          <HStack spaceX={{ base: 2, lg: 3 }} ml="auto">
            <Button
              size="sm"
              onClick={saveForm}
              loading={loading}
              disabled={loading}
            >
              <HStack>
                <FiSave />
                <Text fontSize={{ base: '12px', md: '16px' }}>
                  {loading ? 'Saving...' : 'Save Form'}
                </Text>
              </HStack>
            </Button>
            <IconButton
              variant="ghost"
              size="sm"
              display={{ base: 'flex', lg: 'none' }}
              onClick={onSidebarOpen}
            >
              <FiMenu />
            </IconButton>
          </HStack>
        </HStack>
      </Box>

      <Flex h="calc(100vh - 80px)">
        {/* Desktop Sidebar */}
        <Box
          display={{ base: 'none', lg: 'block' }}
          w="320px"
          bg="white"
          borderRight="1px"
          borderColor="gray.200"
          borderLeft="1px"
          shadow="sm"
        >
          <FormSidebarContent
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            formik={formik}
            formErrors={formErrors}
            org={org}
            env={env}
            qtTypes={qtTypes}
            addQuestion={addQuestion}
            addSection={addSection}
            sidebarOpen={sidebarOpen}
            onSidebarClose={onSidebarClose}
            pages={pages}
            currentPageIndex={currentPageIndex}
            setCurrentPageIndex={setCurrentPageIndex}
            totalQuestions={totalQuestions}
            totalSections={totalSections}
            syncedFields={syncedFields}
            applySyncedFields={applySyncedFields}
            isQuestion={isQuestion}
            isSection={isSection}
            addPage={addPage}
            deletePage={deletePage}
          />
        </Box>

        {/* Mobile Drawer */}
        <DrawerRoot
          open={sidebarOpen}
          onOpenChange={({ open }) =>
            open ? onSidebarOpen() : onSidebarClose()
          }
          placement={'start'}
        >
          <DrawerBackdrop />
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle textTransform={'capitalize'}>
                {FormData?.title || 'Untitled Form'}
              </DrawerTitle>
            </DrawerHeader>
            <Box p={0} flex={1}>
              <FormSidebarContent
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                formik={formik}
                formErrors={formErrors}
                org={org}
                env={env}
                qtTypes={qtTypes}
                addQuestion={addQuestion}
                addSection={addSection}
                sidebarOpen={sidebarOpen}
                onSidebarClose={onSidebarClose}
                pages={pages}
                currentPageIndex={currentPageIndex}
                setCurrentPageIndex={setCurrentPageIndex}
                totalQuestions={totalQuestions}
                totalSections={totalSections}
                syncedFields={syncedFields}
                applySyncedFields={applySyncedFields}
                isQuestion={isQuestion}
                isSection={isSection}
                addPage={addPage}
                deletePage={deletePage}
              />
            </Box>
          </DrawerContent>
        </DrawerRoot>

        {/* Main Content */}
        <Flex flex={1} direction={{ base: 'column', lg: 'row' }}>
          {/* Form Preview */}
          <Box flex={1} bg="gray.50" p={{ base: 2, lg: 3 }} overflowY="auto">
            <Container maxW="3xl">
              {/* Page Navigation */}
              <HStack justify="between" mb={6}>
                <HStack spaceX={4}>
                  <IconButton
                    variant="outline"
                    _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                    size="sm"
                    disabled={currentPageIndex === 0}
                    onClick={() => setCurrentPageIndex(currentPageIndex - 1)}
                  >
                    <FiChevronLeft />
                  </IconButton>
                  <Text fontSize="sm" fontWeight="medium">
                    Page {currentPageIndex + 1} of {pages.length}
                  </Text>
                  <IconButton
                    variant="outline"
                    size="sm"
                    _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                    disabled={currentPageIndex === pages.length - 1}
                    onClick={() => setCurrentPageIndex(currentPageIndex + 1)}
                  >
                    <FiChevronRight />
                  </IconButton>
                </HStack>
                <Badge variant="outline">
                  {currentPage.items.length} item(s)
                </Badge>
              </HStack>

              {/* Current Page Items */}
              <Card.Root borderColor={'gray.50'}>
                <Card.Header>
                  <Heading size="lg">{currentPage.title}</Heading>
                  {currentPage.description && (
                    <Text fontSize="sm" color="gray.600">
                      {currentPage.description}
                    </Text>
                  )}
                </Card.Header>
                <Card.Body spaceY={6}>
                  {currentPage.items.length === 0 ? (
                    <VStack py={12} spaceY={4}>
                      <Box
                        p={6}
                        rounded="full"
                        bg="gray.100"
                        border="2px dashed"
                        borderColor="gray.300"
                      >
                        <FiFileText size={48} color="gray.400" />
                      </Box>
                      <VStack spaceY={2} textAlign="center">
                        <Heading size="md" color="gray.600">
                          No items on this page
                        </Heading>
                        <Text fontSize="sm" color="gray.500" maxW="sm">
                          Start building your form by adding questions.
                        </Text>
                      </VStack>
                      <HStack spaceY={0} mt={4}>
                        {/* <Text fontSize="xs" color="gray.400">
                          💡 Tip: Use Sync for quick setup
                        </Text> */}
                      </HStack>
                    </VStack>
                  ) : (
                    <>
                      {currentPage.items.map((item, index) => (
                        <Box key={item.id} position="relative">
                          {/* Drop indicator line */}
                          {draggedItem &&
                            draggedItem.id !== item.id &&
                            dropZoneIndex === index && (
                              <Box
                                position="absolute"
                                top="-6px"
                                right="-24px"
                                w="4px"
                                h="calc(100% + 12px)"
                                bg="primary.400"
                                borderRadius="full"
                                zIndex={10}
                              />
                            )}

                          <Box
                            p={4}
                            rounded="lg"
                            border="1px"
                            borderColor={
                              selectedItem?.id === item.id
                                ? 'primary.300'
                                : isQuestion(item) &&
                                    formErrors.qts.includes(item.id)
                                  ? 'red.300'
                                  : 'gray.200'
                            }
                            bg={
                              selectedItem?.id === item.id
                                ? 'primary.50'
                                : isQuestion(item) &&
                                    formErrors.qts.includes(item.id)
                                  ? 'red.50'
                                  : 'white'
                            }
                            shadow="sm"
                            cursor="pointer"
                            _hover={{
                              bg: 'primary.50',
                              borderColor:
                                selectedItem?.id === item.id
                                  ? 'primary.300'
                                  : 'primary.300',
                              shadow: 'md',
                            }}
                            onClick={() => setSelectedItem(item)}
                            draggable={
                              isSection(item) ||
                              (isQuestion(item) && item.default !== 'true')
                            }
                            onDragStart={(e) => handleDragStart(e, item)}
                            onDragOver={(e) => handleDragOver(e, index)}
                            onDragLeave={handleDragLeave}
                            onDrop={(e) => handleDrop(e, index)}
                          >
                            <Flex
                              align="start"
                              justify="space-between"
                              width="100%"
                            >
                              <VStack
                                align="start"
                                flex={1}
                                minW={0}
                                overflow="hidden"
                                spaceY={2}
                              >
                                {isSection(item) ? (
                                  <>
                                    <Flex
                                      justifyContent={'space-between'}
                                      width={'100%'}
                                      alignItems={'center'}
                                      mb={1}
                                    >
                                      <HStack spaceX={2}>
                                        <FiLayers color="purple.600" />
                                        <Badge fontSize="xs">Section</Badge>
                                      </HStack>
                                      <Box
                                        display={'flex'}
                                        alignItems={'center'}
                                        gap={2}
                                      >
                                        {(isSection(item) ||
                                          isQuestion(item)) && (
                                          <>
                                            <FiMove
                                              color="gray.400"
                                              cursor="move"
                                            />

                                            <IconButton
                                              size="sm"
                                              variant="ghost"
                                              _hover={{
                                                bg: 'primary.100',
                                              }}
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                deleteItem(item.id);
                                              }}
                                              colorScheme="red"
                                            >
                                              <FiTrash2 />
                                            </IconButton>
                                          </>
                                        )}
                                      </Box>
                                    </Flex>

                                    <Heading size="lg" color="gray.900">
                                      {item.title || 'Untitled Section'}
                                    </Heading>
                                    {item.description && (
                                      <Text fontSize="sm" color="gray.600">
                                        {item.description}
                                      </Text>
                                    )}
                                  </>
                                ) : (
                                  <>
                                    <Flex
                                      width="100%"
                                      justify="space-between"
                                      alignItems={'flex-start'}
                                    >
                                      <Flex
                                        w={'100%'}
                                        flexDirection={'column'}
                                        alignItems={'flex-start'}
                                        gap={1}
                                      >
                                        <Text
                                          fontSize="sm"
                                          fontWeight="medium"
                                          flex={1}
                                          minW={0}
                                          mr={2}
                                        >
                                          {item.qt}
                                          {item.required && (
                                            <Text
                                              as="span"
                                              color="red.500"
                                              ml={1}
                                            >
                                              *
                                            </Text>
                                          )}
                                        </Text>

                                        <Flex alignItems={'center'} gap={1}>
                                          {/* Synced Badge */}
                                          {isQuestion(item) && item.synced && (
                                            <HStack spaceX={1}>
                                              <Badge
                                                variant="solid"
                                                fontSize="xs"
                                                colorScheme="primary"
                                                bg="primary.500"
                                                color="white"
                                              >
                                                Synced
                                              </Badge>
                                            </HStack>
                                          )}
                                          {/* Notable Date Badge for date questions */}
                                          {isQuestion(item) &&
                                            item.type === 'Date' &&
                                            item.notableDate && (
                                              <Badge
                                                variant="outline"
                                                fontSize="xs"
                                                color="purple.600"
                                                borderColor="purple.300"
                                              >
                                                Notable Date
                                              </Badge>
                                            )}
                                        </Flex>
                                      </Flex>

                                      <HStack flexShrink={0} spaceX={2}>
                                        {isQuestion(item) &&
                                          item.default === 'true' && (
                                            <HStack spaceX={2}>
                                              <FiLock
                                                color="orange.500"
                                                size={12}
                                              />
                                              <Badge
                                                variant="outline"
                                                fontSize="xs"
                                                color="orange.600"
                                                borderColor="orange.300"
                                              >
                                                Default
                                              </Badge>
                                            </HStack>
                                          )}

                                        <Box
                                          display={'flex'}
                                          alignItems={'center'}
                                          gap={2}
                                          mt={'-8px'}
                                        >
                                          {(isSection(item) ||
                                            (isQuestion(item) &&
                                              item.default !== 'true')) && (
                                            <>
                                              <FiMove
                                                color="gray.400"
                                                cursor="move"
                                              />

                                              <IconButton
                                                size="sm"
                                                variant="ghost"
                                                _hover={{
                                                  bg: 'primary.100',
                                                }}
                                                onClick={(e) => {
                                                  e.stopPropagation();
                                                  deleteItem(item.id);
                                                }}
                                                colorScheme="red"
                                              >
                                                <FiTrash2 />
                                              </IconButton>
                                            </>
                                          )}
                                        </Box>
                                      </HStack>
                                    </Flex>

                                    {item.description && (
                                      <Text fontSize="xs" color="gray.500">
                                        {item.description}
                                      </Text>
                                    )}

                                    {/* Show event info for notable dates */}
                                    {isQuestion(item) &&
                                      item.type === 'Date' &&
                                      item.notableDate &&
                                      item.event && (
                                        <Text fontSize="xs" color="purple.600">
                                          Event:{' '}
                                          {item.event === 'OTHER'
                                            ? item.other || 'Other'
                                            : eventOptions.find(
                                                (opt) =>
                                                  opt.value === item.event
                                              )?.label || item.event}
                                        </Text>
                                      )}

                                    {/* Question preview - Full width */}
                                    <Box mt={2} width="100%">
                                      {renderQuestionPreview(item)}
                                    </Box>

                                    {isQuestion(item) &&
                                      formErrors.qts.includes(item.id) && (
                                        <Text fontSize="xs" color="red.500">
                                          Question text cannot be empty
                                        </Text>
                                      )}
                                  </>
                                )}
                              </VStack>
                            </Flex>
                          </Box>
                        </Box>
                      ))}

                      {/* Drop indicator line at the end */}
                      {draggedItem &&
                        dropZoneIndex === currentPage.items.length && (
                          <Box position="relative" h="1px">
                            <Box
                              position="absolute"
                              top="-6px"
                              right="-24px"
                              w="4px"
                              h="12px"
                              bg="blue.400"
                              borderRadius="full"
                              zIndex={10}
                            />
                          </Box>
                        )}

                      {/* Invisible drop zone at the end */}
                      {draggedItem && (
                        <Box
                          h="20px"
                          onDragOver={(e) =>
                            handleDragOver(e, currentPage.items.length)
                          }
                          onDragLeave={handleDragLeave}
                          onDrop={(e) =>
                            handleDrop(e, currentPage.items.length)
                          }
                        />
                      )}
                    </>
                  )}
                </Card.Body>
              </Card.Root>
            </Container>
          </Box>

          {/* Right Panel - Item Settings */}
          {selectedItem && (
            <Box
              w={{ base: 'full', lg: '320px' }}
              bg="white"
              borderTop={{ base: '1px', lg: 'none' }}
              borderLeft="1px"
              borderRight="1px"
              borderColor="gray.200"
              shadow="sm"
              p={4}
              maxH={{ base: '96', lg: 'none' }}
              overflowY="auto"
            >
              <HStack justify="between" mb={4} width={'100%'} display={'flex'}>
                <Text minW={'83%'}>
                  {isSection(selectedItem)
                    ? 'Section Settings'
                    : 'Question Settings'}
                </Text>
                <Box>
                  <Button
                    size="sm"
                    variant="ghost"
                    _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                    onClick={() => setSelectedItem(null)}
                  >
                    <FiX />
                  </Button>
                </Box>
              </HStack>

              <VStack spaceY={4} align="stretch">
                {isQuestion(selectedItem) &&
                  selectedItem.default === 'true' && (
                    <Box
                      p={3}
                      bg="primary.50"
                      border="1px"
                      borderColor="primary.200"
                      borderRadius="lg"
                    >
                      <HStack mb={2}>
                        <FiLock color="primary.500" />
                        <Text
                          fontSize="sm"
                          fontWeight="medium"
                          color="primary.700"
                        >
                          Default Field
                        </Text>
                      </HStack>
                      <Text fontSize="xs" color="primary.600">
                        This email field is required for all forms and cannot be
                        deleted or moved.
                      </Text>
                    </Box>
                  )}

                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={1}>
                    Page Number
                  </Text>
                  <Box
                    p={2}
                    bg="gray.50"
                    rounded="md"
                    border="1px"
                    borderColor="gray.200"
                  >
                    <Text fontSize="sm" color="gray.700">
                      Page {selectedItem.page}
                    </Text>
                  </Box>
                </Box>

                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={1}>
                    {isSection(selectedItem) ? 'Section Title' : 'Title'}
                  </Text>
                  <Input
                    borderColor={'gray.50'}
                    value={selectedItem.title || ''}
                    onChange={(e) =>
                      updateItem(selectedItem.id, { title: e.target.value })
                    }
                    placeholder={
                      isSection(selectedItem)
                        ? 'Enter section title'
                        : 'Enter title (optional)'
                    }
                  />
                </Box>

                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={1}>
                    Description
                  </Text>
                  <Textarea
                    borderColor={'gray.50'}
                    value={selectedItem.description || ''}
                    onChange={(e) =>
                      updateItem(selectedItem.id, {
                        description: e.target.value,
                      })
                    }
                    rows={2}
                    placeholder="Enter description (optional)"
                  />
                </Box>

                {isQuestion(selectedItem) && (
                  <Box>
                    <Text fontSize="sm" fontWeight="medium" mb={1}>
                      Question{' '}
                      <Text as="span" color="red.500">
                        *
                      </Text>
                    </Text>
                    <Input
                      value={selectedItem.qt}
                      onChange={(e) =>
                        updateItem(selectedItem.id, {
                          qt: e.target.value,
                        })
                      }
                      placeholder="Enter question text"
                      borderColor={
                        formErrors.qts.includes(selectedItem.id)
                          ? 'red.300'
                          : 'gray.50'
                      }
                    />
                    {formErrors.qts.includes(selectedItem.id) && (
                      <Text fontSize="xs" color="red.500" mt={1}>
                        question text cannot be empty
                      </Text>
                    )}
                  </Box>
                )}

                {isQuestion(selectedItem) && (
                  <Box width={'100%'} display={'flex'} justifyContent={'end'}>
                    <HStack justify="between">
                      <Text fontSize="sm" fontWeight="medium">
                        Required
                      </Text>
                      <Switch
                        checked={selectedItem.required}
                        onCheckedChange={(details) =>
                          updateItem(selectedItem.id, {
                            required: details.checked,
                          })
                        }
                        disabled={
                          selectedItem.default === 'true' ||
                          selectedItem.synced ||
                          (selectedItem.type === 'Date' &&
                            selectedItem.notableDate)
                        }
                      />
                    </HStack>
                  </Box>
                )}

                {/* Notable Date Section - Only for date questions */}
                {isQuestion(selectedItem) && selectedItem.type === 'Date' && (
                  <Box width={'100%'} display={'flex'} justifyContent={'end'}>
                    <HStack justify="between">
                      <Text fontSize="sm" fontWeight="medium">
                        Notable Date
                      </Text>
                      <Switch
                        checked={
                          selectedItem.notableDate || selectedItem.synced
                        }
                        onCheckedChange={() =>
                          handleNotableDateToggle(
                            selectedItem.id,
                            selectedItem.notableDate || false
                          )
                        }
                        disabled={selectedItem.synced}
                      />
                    </HStack>
                  </Box>
                )}

                {/* Event Selection - Only for date questions with notable date enabled */}
                {isQuestion(selectedItem) &&
                  selectedItem.type === 'Date' &&
                  (selectedItem.notableDate || selectedItem.synced) && (
                    <Box>
                      <Text fontSize="sm" fontWeight="medium" mb={1}>
                        Event{' '}
                        <Text as="span" color="red.500">
                          *
                        </Text>
                      </Text>
                      <CustomSelect
                        options={eventOptions}
                        value={
                          eventOptions.find(
                            (option) => option.value === selectedItem.event
                          ) || null
                        }
                        onChange={(option) =>
                          updateItem(selectedItem.id, { event: option.value })
                        }
                        placeholder="Select event"
                      />

                      {isQuestion(selectedItem) &&
                        formErrors?.events?.includes(selectedItem?.id) && (
                          <Text fontSize="xs" color="red.500">
                            Please select an event.
                          </Text>
                        )}
                    </Box>
                  )}

                {/* Other Event Input - Only when event is "OTHER" */}
                {isQuestion(selectedItem) &&
                  selectedItem.type === 'Date' &&
                  (selectedItem.notableDate || selectedItem.synced) &&
                  selectedItem.event === 'OTHER' && (
                    <Box>
                      <Text fontSize="sm" fontWeight="medium" mb={1}>
                        Other Event{' '}
                        <Text as="span" color="red.500">
                          *
                        </Text>
                      </Text>
                      <Input
                        borderColor={'gray.50'}
                        value={selectedItem.other || ''}
                        onChange={(e) =>
                          updateItem(selectedItem.id, { other: e.target.value })
                        }
                        placeholder="Enter custom event"
                      />
                    </Box>
                  )}

                {isQuestion(selectedItem) &&
                  (selectedItem.type === 'Single choice' ||
                    selectedItem.type === 'Multi choice') && (
                    <Box>
                      <Text fontSize="sm" fontWeight="medium" mb={2}>
                        Answer Options
                      </Text>
                      <VStack spaceY={2} align="stretch">
                        {selectedItem.options?.map((option, index) => (
                          <HStack key={index}>
                            <Input
                              borderColor={'gray.50'}
                              value={option}
                              onChange={(e) => {
                                const newOptions = [
                                  ...(selectedItem.options || []),
                                ];
                                newOptions[index] = e.target.value;
                                updateItem(selectedItem.id, {
                                  options: newOptions,
                                });
                              }}
                              flex={1}
                              placeholder={`Option ${index + 1}`}
                            />
                            <IconButton
                              size="sm"
                              variant="ghost"
                              colorScheme="red"
                              onClick={() => {
                                const newOptions = selectedItem.options?.filter(
                                  (_, i) => i !== index
                                );
                                updateItem(selectedItem.id, {
                                  options: newOptions,
                                });
                              }}
                              disabled={
                                selectedItem.options?.length === 1 ||
                                selectedItem.synced
                              }
                            >
                              <FiTrash2 />
                            </IconButton>
                          </HStack>
                        ))}
                        <Button
                          size="sm"
                          variant="outline"
                          _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                          onClick={() => {
                            const newOptions = [
                              ...(selectedItem.options || []),
                              `Option ${(selectedItem.options?.length || 0) + 1}`,
                            ];
                            updateItem(selectedItem.id, {
                              options: newOptions,
                            });
                          }}
                          disabled={false}
                        >
                          <HStack>
                            <FiPlus />
                            <Text>Add Option</Text>
                          </HStack>
                        </Button>
                      </VStack>
                    </Box>
                  )}

                {isQuestion(selectedItem) &&
                  selectedItem.default !== 'true' && (
                    <Box>
                      <Text fontSize="sm" fontWeight="medium" mb={1}>
                        Move to Page
                      </Text>
                      <CustomSelect
                        options={pages.map((page, index) => ({
                          value: page.id,
                          label: `Page ${index + 1}: ${page.title}`,
                        }))}
                        value={{
                          value:
                            pages.find((page) =>
                              page.items.some(
                                (item) => item.id === selectedItem.id
                              )
                            )?.id ||
                            selectedItem.pageId ||
                            `page-${selectedItem.page}`,
                          label: (() => {
                            const currentPage = pages.find((page) =>
                              page.items.some(
                                (item) => item.id === selectedItem.id
                              )
                            );
                            if (currentPage) {
                              const pageIndex =
                                pages.findIndex(
                                  (p) => p.id === currentPage.id
                                ) + 1;
                              return `Page ${pageIndex}: ${currentPage.title}`;
                            }
                            return `Page ${selectedItem.page}: ${pages[selectedItem.page - 1]?.title}`;
                          })(),
                        }}
                        onChange={(option) =>
                          moveItem(selectedItem.id, option.value)
                        }
                        placeholder="Select page"
                      />
                    </Box>
                  )}

                {isQuestion(selectedItem) &&
                  selectedItem.default !== 'true' && (
                    <Box pt={4} borderTop="1px" borderColor="gray.200">
                      <Button
                        _hover={{ bg: 'primary.100', borderColor: 'gray.50' }}
                        variant="outline"
                        size="sm"
                        w="full"
                        onClick={() => {
                          const newItem = {
                            ...selectedItem,
                            id: `q${Date.now()}`,
                            qt: `${selectedItem.qt} (Copy)`,
                            title: selectedItem.title
                              ? `${selectedItem.title} (Copy)`
                              : undefined,
                            page: currentPageIndex + 1,
                            pageId: currentPage.id,
                            default: 'false',
                            synced: false,
                            synced_field: undefined,
                            // Reset notable date specific fields for duplicated items
                            notableDate:
                              selectedItem.synced &&
                              selectedItem.type === 'Date'
                                ? true
                                : false,
                            required:
                              selectedItem.synced &&
                              selectedItem.type === 'Date'
                                ? true
                                : selectedItem.required,
                            event: undefined,
                            other: undefined,
                          };

                          // Use formik to update pages
                          const updatedPages = pages.map((page) =>
                            page.id === currentPage.id
                              ? { ...page, items: [...page.items, newItem] }
                              : page
                          );
                          formik.setFieldValue('pages', updatedPages);
                        }}
                        disabled={selectedItem.synced}
                      >
                        <HStack>
                          <FiCopy />
                          <Text>Duplicate question</Text>
                        </HStack>
                      </Button>
                    </Box>
                  )}
              </VStack>
            </Box>
          )}
        </Flex>
      </Flex>
    </Box>
  );
};

export default CForm;
